import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# 启用cudnn自动优化，提升计算性能
torch.backends.cudnn.benchmark = True

class Attention(nn.Module):
    def __init__(self, n_hidden):
        super(Attention, self).__init__()
        self.size = 0
        self.batch_size = 0
        self.dim = n_hidden

        v = torch.FloatTensor(n_hidden).to(device)
        # v:(n_hidden)
        self.v = nn.Parameter(v)
        # 使用Kaiming初始化
        nn.init.kaiming_uniform_(self.v.view(1, -1))

        # parameters for pointer attention
        self.Wref = nn.Linear(n_hidden, n_hidden)
        self.Wq = nn.Linear(n_hidden, n_hidden)
        
        # 添加层归一化提高训练稳定性
        self.layer_norm = nn.LayerNorm(n_hidden)
        
        # 添加dropout
        self.dropout = nn.Dropout(0.1)

        # ref:(B,seq_len,n_hidden)
        # q:(B,n_hidden)

    def forward(self, q, ref):  # query and reference
        self.batch_size = q.size(0)
        self.size = int(ref.size(0) / self.batch_size)
        q = self.Wq(q)  # (B, dim)
        ref = self.Wref(ref)
        ref = ref.view(self.batch_size, self.size, self.dim)  # (B, size, dim)
        q_ex = q.unsqueeze(1).repeat(1, self.size, 1)  # (B, size, dim)
        # v_view: (B, dim, 1)
        v_view = self.v.unsqueeze(0).expand(self.batch_size, self.dim).unsqueeze(2)

        # 添加层归一化和残差连接
        combined = self.layer_norm(q_ex + ref)
        combined = self.dropout(combined)
        
        # (B, size, dim) * (B, dim, 1)
        # u (B, size)
        u = torch.bmm(torch.tanh(combined), v_view).squeeze(2)

        return u, ref

# class MultiHead_Additive_Attention(nn.Module):
#     """MultiHead Additive Attention with Glimpse Mechanism"""
#
#     def __init__(self, hidden_size, n_head, device):
#         super(MultiHead_Additive_Attention, self).__init__()
#         self.hidden_size = hidden_size
#         self.n_head = n_head
#         self.d_hidden_size = hidden_size // n_head
#         self.v = nn.Parameter(torch.zeros((1, 1, self.hidden_size),
#                                           device=device, requires_grad=True))
#         self.W = nn.Parameter(torch.zeros((1, self.d_hidden_size, 2 * self.d_hidden_size),
#                                           device=device, requires_grad=True))
#         self.w_element = nn.Linear(hidden_size, n_head * self.d_hidden_size)
#         self.w_context = nn.Linear(hidden_size, n_head * self.d_hidden_size)
#
#         nn.init.normal_(self.w_element.weight, mean=0, std=np.sqrt(2.0 / (hidden_size + self.d_hidden_size)))
#         nn.init.normal_(self.w_context.weight, mean=0, std=np.sqrt(2.0 / (hidden_size + self.d_hidden_size)))
#
#     def forward(self, element_hidden, context_hidden):
#
#         # element_hidden: (batch_size, hidden_size)
#         # context_hidden: (batch_size, hidden_size, seq_len)
#         n_head, d_hidden_size = self.n_head, self.d_hidden_size
#         batch_size, hidden_size, seq_len = context_hidden.size()
#
#         # element_hidden: (batch_size, hidden_size)
#         element_hidden = element_hidden.unsqueeze(2).expand_as(context_hidden)
#
#         # element_hidden: (batch_size, seq_len, n_head, d_hidden_size)
#         # context_hidden: (batch_size, seq_len, n_head, d_hidden_size)
#         element_hidden = self.w_element(element_hidden.permute(0, 2, 1)).view(batch_size, seq_len, n_head, d_hidden_size)
#         context_hidden = self.w_context(context_hidden.permute(0, 2, 1)).view(batch_size, seq_len, n_head, d_hidden_size)
#
#         # element_hidden: (n_head * batch_size, d_hidden_size, seq_len)
#         # context_hidden: (n_head * batch_size, d_hidden_size, seq_len)
#         element_hidden = element_hidden.permute(2, 0, 3, 1).contiguous().view(-1, d_hidden_size, seq_len)
#         context_hidden = context_hidden.permute(2, 0, 3, 1).contiguous().view(-1, d_hidden_size, seq_len)
#
#         # hidden: (n_head * batch_size, 2* d_hidden_size, seq_len)
#         hidden = torch.cat((element_hidden, context_hidden), dim=1)
#
#         # v: (batch_size, 1, hidden_size)
#         v = self.v.expand(batch_size, -1, -1)
#         # W: (n_head * batch_size, d_hidden_size, 2 * d_hidden_size)
#         W = self.W.expand(n_head * batch_size, -1, -1)
#         # attns: (n_head, batch_size, d_hidden_size, seq_len)
#         attns = torch.tanh(torch.bmm(W, hidden)).view(n_head, batch_size, d_hidden_size, seq_len)
#         # attns: (batch_size, hidden_size, seq_len)
#         attns = attns.permute(1, 0, 2, 3).contiguous().view(batch_size, -1, seq_len)
#         # probs: (batch_size, seq_len)
#         probs = torch.bmm(v, attns).squeeze(1)
#         return probs

class MultiHead_Additive_Attention(nn.Module):
    """MultiHead Additive Attention with Glimpse Mechanism"""

    def __init__(self, hidden_size, n_head=8, dropout=0.1, device=None):
        super(MultiHead_Additive_Attention, self).__init__()
        if device is None:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            
        self.hidden_size = hidden_size
        self.n_head = n_head
        self.d_hidden_size = hidden_size // n_head
        self.dropout_rate = dropout
        
        # 初始化参数
        self.v = nn.Parameter(torch.zeros((1, 1, self.hidden_size), device=device))
        self.W = nn.Parameter(torch.zeros((1, self.d_hidden_size, 2 * self.d_hidden_size), device=device))
        
        # 线性变换层
        self.w_query = nn.Linear(hidden_size, n_head * self.d_hidden_size)
        self.w_context = nn.Linear(hidden_size, n_head * self.d_hidden_size)
        
        # 输出投影层
        self.out_proj = nn.Linear(hidden_size, hidden_size)
        
        # 层归一化
        self.layer_norm1 = nn.LayerNorm(hidden_size)
        self.layer_norm2 = nn.LayerNorm(hidden_size)
        
        # dropout层
        self.dropout = nn.Dropout(dropout)
        self.attn_dropout = nn.Dropout(dropout)

        # 使用Kaiming初始化
        nn.init.kaiming_uniform_(self.w_query.weight)
        nn.init.kaiming_uniform_(self.w_context.weight)
        nn.init.kaiming_uniform_(self.out_proj.weight)
        nn.init.kaiming_uniform_(self.W.view(1, -1, 2 * self.d_hidden_size))
        nn.init.kaiming_uniform_(self.v.view(1, -1, 1))
        
        # 残差连接的缩放因子
        self.scale_factor = nn.Parameter(torch.ones(1))

    def forward(self, q, context):
        # q: (batch_size, hidden_size)
        # context: (batch_size * seq_len, hidden_size)
        batch_size = q.size(0)
        seq_len = context.size(0) // batch_size
        
        # 将context重塑为三维张量
        context = context.view(batch_size, seq_len, -1).transpose(1, 2)  # (batch_size, hidden_size, seq_len)
        
        # 保存原始输入用于残差连接
        q_residual = q
        context_residual = context.transpose(1, 2).contiguous().view(batch_size * seq_len, -1)
        
        # 扩展query以匹配context的序列长度
        q_expanded = q.unsqueeze(2).expand(-1, -1, seq_len)  # (batch_size, hidden_size, seq_len)
        
        # 多头注意力处理
        n_head, d_hidden_size = self.n_head, self.d_hidden_size
        
        # 线性变换并重塑为多头形式
        q_proj = self.w_query(q_expanded.transpose(1, 2)).view(batch_size, seq_len, n_head, d_hidden_size)
        context_proj = self.w_context(context.transpose(1, 2)).view(batch_size, seq_len, n_head, d_hidden_size)
        
        # 应用dropout
        q_proj = self.dropout(q_proj)
        context_proj = self.dropout(context_proj)
        
        # 重排维度以便进行批量矩阵乘法
        q_proj = q_proj.permute(2, 0, 3, 1).contiguous().view(-1, d_hidden_size, seq_len)
        context_proj = context_proj.permute(2, 0, 3, 1).contiguous().view(-1, d_hidden_size, seq_len)
        
        # 拼接特征
        combined = torch.cat((q_proj, context_proj), dim=1)
        
        # 扩展参数以匹配批量大小
        W = self.W.expand(n_head * batch_size, -1, -1)
        v = self.v.expand(batch_size, -1, -1)
        
        # 计算注意力分数
        attns = torch.tanh(torch.bmm(W, combined)).view(n_head, batch_size, d_hidden_size, seq_len)
        attns = attns.permute(1, 0, 2, 3).contiguous().view(batch_size, -1, seq_len)
        
        # 应用注意力dropout
        attns = self.attn_dropout(attns)
        
        # 计算最终的注意力分数
        probs = torch.bmm(v, attns).squeeze(1)
        
        # 应用缩放因子
        probs = probs * self.scale_factor
        
        # 返回注意力分数和上下文
        return probs, context

class Additive_Attention_Glimpse(nn.Module):
    # Calculates attention over the input nodes given the current state
    def __init__(self, hidden_size, dropout=0.1):
        super(Additive_Attention_Glimpse, self).__init__()
        self.v = nn.Parameter(torch.zeros((1, 1, hidden_size), device=device))
        self.W = nn.Parameter(torch.zeros((1, hidden_size, 3 * hidden_size), device=device))

        self.v_glimpse = nn.Parameter(torch.zeros((1, 1, hidden_size), device=device))
        self.W_glimpse = nn.Parameter(torch.zeros((1, hidden_size, 2 * hidden_size), device=device))
        
        # 添加层归一化
        self.layer_norm1 = nn.LayerNorm(hidden_size)
        self.layer_norm2 = nn.LayerNorm(hidden_size)
        
        # 添加dropout
        self.dropout = nn.Dropout(dropout)
        self.attn_dropout = nn.Dropout(dropout)
        
        # 初始化参数
        nn.init.kaiming_uniform_(self.v.view(1, -1, 1))
        nn.init.kaiming_uniform_(self.W.view(1, -1, 3 * hidden_size))
        nn.init.kaiming_uniform_(self.v_glimpse.view(1, -1, 1))
        nn.init.kaiming_uniform_(self.W_glimpse.view(1, -1, 2 * hidden_size))
        
        # 残差连接的缩放因子
        self.scale_factor = nn.Parameter(torch.ones(1))

    def forward(self, static_hidden, dynamic_hidden, decoder_hidden):
        batch_size, hidden_size, _ = static_hidden.size()
        # hidden: (batch_size, hidden_size, seq_len)
        hidden = decoder_hidden.unsqueeze(2).expand_as(static_hidden)
        # hidden: (batch_size, 3 * hidden_size, seq_len)
        hidden = torch.cat((static_hidden, dynamic_hidden, hidden), 1)
        # Broadcast some dimensions so we can do batch-matrix-multiply
        # v: (batch_size, 1, hidden_size)
        v = self.v.expand(batch_size, 1, hidden_size)
        # W: (batch_size, hidden_size, 3 * hidden_size)
        W = self.W.expand(batch_size, hidden_size, -1)
        
        # 应用dropout
        hidden = self.dropout(hidden)
        
        # attns: (batch_size, 1, seq_len)
        attns = torch.bmm(v, torch.tanh(torch.bmm(W, hidden)))
        # 应用注意力dropout
        attns = self.attn_dropout(attns)
        # attns: (batch_size, 1, seq_len)
        attns = F.softmax(attns, dim=2)
        # context: (batch_size, 1, hidden_size)
        context = attns.bmm(static_hidden.permute(0, 2, 1))
        # Calculate the next output using Batch-matrix-multiply ops
        # context: (batch_size, hidden_size, seq_len)
        context = context.transpose(1, 2).expand_as(static_hidden)
        
        # 添加层归一化
        static_norm = self.layer_norm1(static_hidden.permute(0, 2, 1)).permute(0, 2, 1)
        context_norm = self.layer_norm2(context.permute(0, 2, 1)).permute(0, 2, 1)
        
        # energy: (batch_size, 2 * hidden_size, seq_len)
        energy = torch.cat((static_norm, context_norm), dim=1)
        # 应用dropout
        energy = self.dropout(energy)
        # v: (batch_size, 1, hidden_size)
        v_glimpse = self.v_glimpse.expand(static_hidden.size(0), -1, -1)
        # W: (batch_size, hidden_size, 2 * hidden_size)
        W_glimpse = self.W_glimpse.expand(static_hidden.size(0), -1, -1)
        # probs: (batch_size, seq_len)
        probs = torch.bmm(v_glimpse, torch.tanh(torch.bmm(W_glimpse, energy))).squeeze(1)
        # 应用缩放因子
        probs = probs * self.scale_factor
        
        return probs
