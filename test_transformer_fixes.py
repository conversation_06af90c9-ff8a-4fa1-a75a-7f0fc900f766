"""
Transformer模型修复验证测试脚本
验证修复后的Transformer模型是否正常工作
"""
import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.gpn_transformer import GPNTransformer, TransformerPointerNetwork
from constellation_smp.transformer_encoder import ConstellationTransformerEncoder
from constellation_smp.constellation_smp import ConstellationSMPDataset

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

def test_transformer_pointer_network():
    """测试TransformerPointerNetwork的维度修复"""
    print("\n=== 测试 TransformerPointerNetwork ===")
    
    batch_size, d_model, seq_len = 32, 128, 100
    
    # 创建测试数据
    query = torch.randn(batch_size, d_model, device=device)
    context = torch.randn(batch_size, d_model, seq_len, device=device)
    mask = torch.ones(batch_size, seq_len, device=device)
    
    # 创建模型
    pointer_net = TransformerPointerNetwork(d_model, n_heads=8, dropout=0.1).to(device)
    
    try:
        # 前向传播
        logits = pointer_net(query, context, mask)
        
        # 验证输出维度
        expected_shape = (batch_size, seq_len)
        assert logits.shape == expected_shape, f"输出维度错误: 期望 {expected_shape}, 实际 {logits.shape}"
        
        # 验证数值稳定性
        assert torch.isfinite(logits).all(), "输出包含非有限值"
        
        print("✅ TransformerPointerNetwork 测试通过")
        print(f"   输入维度: query={query.shape}, context={context.shape}")
        print(f"   输出维度: {logits.shape}")
        print(f"   输出范围: [{logits.min().item():.3f}, {logits.max().item():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ TransformerPointerNetwork 测试失败: {e}")
        return False

def test_constellation_transformer_encoder():
    """测试ConstellationTransformerEncoder的修复"""
    print("\n=== 测试 ConstellationTransformerEncoder ===")
    
    batch_size, static_size, dynamic_size, seq_len, num_satellites = 16, 9, 7, 50, 3
    d_model = 128
    
    # 创建测试数据
    static = torch.randn(batch_size, static_size, seq_len, device=device)
    dynamic = torch.randn(batch_size, dynamic_size, seq_len, num_satellites, device=device)
    
    # 创建编码器
    encoder = ConstellationTransformerEncoder(
        input_size=static_size + dynamic_size,
        d_model=d_model,
        n_heads=8,
        n_layers=4,
        d_ff=512,
        max_len=1000,
        dropout=0.1,
        num_satellites=num_satellites,
        constellation_mode='cooperative'
    ).to(device)
    
    try:
        # 前向传播
        constellation_features, satellite_features = encoder(static, dynamic)
        
        # 验证输出维度
        expected_const_shape = (batch_size, d_model, seq_len)
        expected_sat_shape = (batch_size, d_model, seq_len, num_satellites)
        
        assert constellation_features.shape == expected_const_shape, \
            f"星座特征维度错误: 期望 {expected_const_shape}, 实际 {constellation_features.shape}"
        assert satellite_features.shape == expected_sat_shape, \
            f"卫星特征维度错误: 期望 {expected_sat_shape}, 实际 {satellite_features.shape}"
        
        # 验证数值稳定性
        assert torch.isfinite(constellation_features).all(), "星座特征包含非有限值"
        assert torch.isfinite(satellite_features).all(), "卫星特征包含非有限值"
        
        print("✅ ConstellationTransformerEncoder 测试通过")
        print(f"   输入维度: static={static.shape}, dynamic={dynamic.shape}")
        print(f"   星座特征: {constellation_features.shape}")
        print(f"   卫星特征: {satellite_features.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ ConstellationTransformerEncoder 测试失败: {e}")
        return False

def test_gpn_transformer_full():
    """测试完整的GPNTransformer模型"""
    print("\n=== 测试 GPNTransformer 完整模型 ===")
    
    batch_size, static_size, dynamic_size, seq_len, num_satellites = 8, 9, 7, 30, 3
    d_model = 64
    
    # 创建测试数据
    static = torch.randn(batch_size, static_size, seq_len, device=device)
    dynamic = torch.randn(batch_size, dynamic_size, seq_len, num_satellites, device=device)
    
    # 简单的更新函数（用于测试）
    def simple_update_fn(static, dynamic, chosen_task, chosen_satellite):
        return dynamic
    
    def simple_mask_fn(dynamic, static, chosen_task, chosen_satellite):
        batch_size, _, seq_len, _ = dynamic.shape
        mask = torch.ones(batch_size, seq_len, device=dynamic.device)
        # 简单地将选中的任务设为不可选
        for b in range(batch_size):
            if chosen_task[b] < seq_len:
                mask[b, chosen_task[b]] = 0
        return mask
    
    # 创建模型
    model = GPNTransformer(
        static_size=static_size,
        dynamic_size=dynamic_size,
        d_model=d_model,
        n_heads=8,
        n_layers=2,
        num_satellites=num_satellites,
        update_fn=simple_update_fn,
        mask_fn=simple_mask_fn,
        num_nodes=seq_len,
        dropout=0.1,
        constellation_mode='cooperative'
    ).to(device)
    
    try:
        # 前向传播
        tour_indices, satellite_indices, tour_logp = model(static, dynamic)
        
        # 验证输出维度
        expected_shape = (batch_size, seq_len)
        assert tour_indices.shape == expected_shape, \
            f"任务索引维度错误: 期望 {expected_shape}, 实际 {tour_indices.shape}"
        assert satellite_indices.shape == expected_shape, \
            f"卫星索引维度错误: 期望 {expected_shape}, 实际 {satellite_indices.shape}"
        assert tour_logp.shape == expected_shape, \
            f"对数概率维度错误: 期望 {expected_shape}, 实际 {tour_logp.shape}"
        
        # 验证数值范围
        assert (tour_indices >= 0).all() and (tour_indices < seq_len).all(), "任务索引超出范围"
        assert (satellite_indices >= 0).all() and (satellite_indices < num_satellites).all(), "卫星索引超出范围"
        assert torch.isfinite(tour_logp).all(), "对数概率包含非有限值"
        
        print("✅ GPNTransformer 完整模型测试通过")
        print(f"   输入维度: static={static.shape}, dynamic={dynamic.shape}")
        print(f"   任务索引: {tour_indices.shape}, 范围: [{tour_indices.min()}, {tour_indices.max()}]")
        print(f"   卫星索引: {satellite_indices.shape}, 范围: [{satellite_indices.min()}, {satellite_indices.max()}]")
        print(f"   对数概率: {tour_logp.shape}, 范围: [{tour_logp.min().item():.3f}, {tour_logp.max().item():.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ GPNTransformer 完整模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """测试性能改进"""
    print("\n=== 性能测试 ===")
    
    batch_size, seq_len, num_satellites = 32, 100, 3
    d_model = 128
    
    # 创建较大的测试数据
    static = torch.randn(batch_size, 9, seq_len, device=device)
    dynamic = torch.randn(batch_size, 7, seq_len, num_satellites, device=device)
    
    encoder = ConstellationTransformerEncoder(
        input_size=16, d_model=d_model, n_heads=8, n_layers=4,
        num_satellites=num_satellites, constellation_mode='cooperative'
    ).to(device)
    
    # 预热
    for _ in range(3):
        _ = encoder(static, dynamic)
    
    # 性能测试
    import time
    torch.cuda.synchronize() if device.type == 'cuda' else None
    
    start_time = time.time()
    num_runs = 10
    
    for _ in range(num_runs):
        constellation_features, satellite_features = encoder(static, dynamic)
    
    torch.cuda.synchronize() if device.type == 'cuda' else None
    end_time = time.time()
    
    avg_time = (end_time - start_time) / num_runs
    print(f"✅ 平均推理时间: {avg_time*1000:.2f} ms")
    print(f"   批次大小: {batch_size}, 序列长度: {seq_len}")
    print(f"   内存使用: {torch.cuda.memory_allocated()/1024**2:.1f} MB" if device.type == 'cuda' else "   CPU模式")

def main():
    """主测试函数"""
    print("🚀 开始Transformer模型修复验证测试")
    print("=" * 60)
    
    tests = [
        test_transformer_pointer_network,
        test_constellation_transformer_encoder,
        test_gpn_transformer_full,
        test_performance_comparison
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Transformer模型修复成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
