import scipy.io as sio
import numpy as np
import torch

np.random.seed(123)
torch.manual_seed(123)
REQUIRE_TIME_START = 0.015
REQUIRE_TIME_END = 0.030  # 任务执行所需时间 [0.015,0.030]

REVENUE_START = 1
REVENUE_END = 9  # 收益[1,9]

# MEMORY_CONSUME = 0.01  # 内存消耗
POWER_CONSUME = 0.01  # 能量消耗

# Satellite parameters
Memory = 10
Power = 10
MOVING_TIME_RATE = 0.1  #

STABLE_TIME = 0.005
MOVING_CONSUME_POWER_RATE = 0.01
MEMORY_CONSUME_RATE = 0.002

# 读取文件
load_path = "C:/Users/<USER>/Desktop/single_smp_validation/Schedul_input-4000.mat"
load_data = sio.loadmat(load_path)
load_matrix = load_data['Schedul_input']
have_tw_data = []
# load_matrix_row = load_matrix[0][0][0]
for i in range(4000):
    if load_matrix[0][0][i][1] == 0:
        continue
    if load_matrix[0][0][i][3] > 25 or load_matrix[0][0][i][3] < -25:
        continue
    else:
        have_tw_data.append(load_matrix[0][0][i])
        # 指定数量
        # if len(have_tw_data) == 200:
        #     break


have_tw_data = np.array(have_tw_data)
valid_num = have_tw_data[:,0].astype(int)
# print('valid_num',valid_num)
start_time = []
end_time = []
position = []
seq_len = np.size(have_tw_data, axis=0)
# print('seq_len',seq_len)
for i in range(np.size(have_tw_data, axis=0)):
    # print(have_tw_data[i][1], i)
    start_time.append(have_tw_data[i][1])
    # print(have_tw_data[i][2], i)
    end_time.append(have_tw_data[i][2])
    # print(have_tw_data[i][3], i)
    position.append(have_tw_data[i][3])

start_time = torch.tensor(start_time)
end_time = torch.tensor(end_time)
position = torch.tensor(position)

# 静态元素
static_size = (1, 1, seq_len)
start_time = start_time.reshape(1, 1, -1) / 1000
end_time = end_time.reshape(1, 1, -1) / 1000
position = position.reshape(1, 1, -1) / 100

start_time.float()
end_time.float()
position.float()

require_time = torch.from_numpy(
    np.random.uniform(REQUIRE_TIME_START, REQUIRE_TIME_END, size=static_size)).float()
revenue = torch.from_numpy(np.random.randint(REVENUE_START, REVENUE_END, size=static_size) / 10).float()
memory_consume = require_time * MEMORY_CONSUME_RATE
# memory_consume = torch.from_numpy(np.random.uniform(0, MEMORY_CONSUME, size=static_size)).float()
power_consume = require_time * MOVING_CONSUME_POWER_RATE
# power_consume = torch.from_numpy(np.random.uniform(0, POWER_CONSUME, size=static_size)).float()
static = torch.cat((start_time, position, end_time, require_time, revenue,
                    memory_consume, power_consume), dim=1)

# 动态元素

dynamic_size = (1, 1, seq_len)
time_window = torch.from_numpy(np.ones(dynamic_size)).float()
access = torch.from_numpy(np.ones(dynamic_size)).float()
memory_surplus = torch.from_numpy(np.ones(dynamic_size) * Memory).float()
power_surplus = torch.from_numpy(np.ones(dynamic_size) * Power).float()
last_task = torch.from_numpy(np.zeros(dynamic_size)).float()
start_execution = torch.from_numpy(np.zeros(dynamic_size)).float()
dynamic = torch.cat((time_window, access, memory_surplus,
                     power_surplus, last_task, start_execution), dim=1)

static = static.float()
dynamic = dynamic.float()
static[:, :, 0] = 0.


def update_dynamic(static, dynamic, chosen_idx):
    """
    static: (batch_size, input_size, seq_len)
    dynamic: (batch_size, input_size, seq_len)
    chosen_idx: (batch_size,)
    """
    # last_task: (batch_size, 1)
    last_task = dynamic[:, 4, 0].clone().long().unsqueeze(1)
    # last_start_time: (batch_size, 1)
    last_start_time = dynamic[:, 5, 0].clone().unsqueeze(1)
    # last_position: (batch_size, 1)
    last_position = torch.gather(static[:, 1, :].clone(), 1, last_task)  # gather 收集输入的特定维度指定位置的数值 输出形状和idex一样
    # last_need_time: (batch_size, 1)
    last_need_time = torch.gather(static[:, 3, :].clone(), 1, last_task)

    # chosen_idx: (batch_size, 1)
    chosen_idx = chosen_idx.unsqueeze(1)
    # current_start_time_window: (batch_size, 1)
    current_start_time_window = torch.gather(static[:, 0, :].clone(), 1, chosen_idx)
    # current_position: (batch_size, 1)
    current_position = torch.gather(static[:, 1, :].clone(), 1, chosen_idx)
    # current_need_time: (batch_size, 1)
    current_need_time = torch.gather(static[:, 3, :].clone(), 1, chosen_idx)

    # last_end_time: (batch_size, 1)
    last_end_time = torch.add((MOVING_TIME_RATE * torch.abs(current_position - last_position)),
                              torch.add(last_start_time, last_need_time))

    # two kind of situation
    # 1` current_start_time > last_end_time  current_start_time = current_start_time
    # 2` current_start_time < last_end_time  current_start_time = last_end_time
    # judge_result: (batch_size, 1)
    judge_result = current_start_time_window.ge(
        last_end_time).float()  # torch.ge(input) 对比每一个input和other是否有如下关系input ≥ other
    current_start_time = torch.add(torch.mul((1 - judge_result), last_end_time),
                                   torch.mul(judge_result, current_start_time_window))  # mul 元素对应相乘 mm 矩阵相乘

    # last_task: (batch_size, 1, seq_len)
    last_task = chosen_idx.unsqueeze(2).expand(-1, -1, dynamic.size(2)).float()  # expand 扩充tensor-1 表示当前维度不变；
    # last_start_time: (batch_size, 1, seq_len)
    last_start_time = current_start_time.unsqueeze(2).expand(-1, -1, dynamic.size(2)).float()

    # next_position: (batch_size, seq_len)
    next_position = static[:, 1, :].clone()
    # next_need_time: (batch_size, seq_len)
    next_need_time = static[:, 3, :].clone()
    # next_end_time: (batch_size, seq_len)
    next_end_time = static[:, 2, :].clone()

    # satisfy_time: (batch_size, seq_len)
    # next_end_time >   current_start_time + current_need_time +
    #                   0.2 * |next_position - current_position| + next_need_time
    satisfy_time = torch.add(torch.add((MOVING_TIME_RATE * torch.abs(next_position - current_position)),
                                       torch.add(current_start_time, current_need_time)), next_need_time)

    # satisfy_time_window: (batch_size, seq_len)
    satisfy_time_window = next_end_time.ge(satisfy_time)
    # have_time_window: (batch_size, 1)
    have_time_window = dynamic[:, 0, :].clone().sum(1).gt(0).unsqueeze(1)  # sum(dim),按维度求和 gt 严格大于 返回1
    # time_window: (batch_size, 1, seq_len)
    time_window = torch.mul(satisfy_time_window, have_time_window).unsqueeze(1).float()  # mul 维数可以自动扩充

    # access: (batch_size, seq_len)
    access = dynamic[:, 1, :].clone()
    # chosen_idx: (batch_size,)
    access.scatter_(1, chosen_idx, 0)
    # access: (batch_size, 1, seq_len)
    access = access.unsqueeze(1)

    # next_memory_consume: (batch_size, seq_len)
    next_memory_consume = static[:, 5, :].clone()
    # current_memory_surplus: (batch_size, seq_len)
    current_memory_surplus = dynamic[:, 2, :].clone()
    # satisfy_memory: (batch_size, seq_len)
    satisfy_memory = torch.sub(current_memory_surplus, next_memory_consume)
    satisfy_memory = torch.clamp(satisfy_memory, -1, Memory)
    # satisfy_memory_now: (batch_size, 1, seq_len)
    satisfy_memory_now = satisfy_memory.ge(0).unsqueeze(1).float()
    # memory_consume: (batch_size, 1)
    memory_consume = torch.gather(static[:, 5, :].clone(), 1, chosen_idx)
    # memory_surplus: (batch_size, seq_len)
    memory_surplus = torch.sub(dynamic[:, 2, :].clone(), memory_consume)
    # memory_surplus: (batch_size, 1, seq_len)
    memory_surplus = torch.clamp(memory_surplus, 0.0, Memory).unsqueeze(1)  # clamp:控制在范围内，0.0为下限

    # next_memory_surplus: (batch_size, seq_len)
    next_memory_surplus = memory_surplus.squeeze(1)
    # n_next_memory_consume: (batch_size, seq_len)
    n_next_memory_consume = static[:, 5, :].clone()
    next_satisfy_memory = torch.sub(next_memory_surplus, n_next_memory_consume)
    next_satisfy_memory = torch.clamp(next_satisfy_memory, -1, Memory)
    # satisfy_memory_next: (batch_size, 1, seq_len)
    satisfy_memory_next = next_satisfy_memory.ge(0).unsqueeze(1).float()
    # satisfy_memory: (batch_size, 1, seq_len)
    satisfy_memory = torch.add(satisfy_memory_now, satisfy_memory_next)
    time_window = torch.mul(time_window, satisfy_memory)

    # task_power_consume: (batch_size, 1)
    task_power_consume = torch.gather(static[:, 6, :].clone(), 1, chosen_idx)
    # move_power_consume: (batch_size, 1)
    move_power_consume = MOVING_CONSUME_POWER_RATE * torch.abs(current_position - last_position)  # 机动过程中所消耗的电量

    # power_consume: (batch_size, 1)
    power_consume = torch.add(task_power_consume, move_power_consume)
    # power_surplus: (batch_size, seq_len)
    power_surplus = dynamic[:, 3, :].clone() - power_consume
    # power_surplus: (batch_size, 1, seq_len)
    power_surplus = torch.clamp(power_surplus, 0.0, Power).unsqueeze(1)
    # have_power_surplus: (batch_size, 1)
    have_power_surplus = torch.neg(power_surplus[:, 0, :].sum(1).gt(0).float())
    # have_no_power_surplus: (batch_size, 1)
    have_no_power_surplus = torch.add(1.0, have_power_surplus).nonzero()
    # time_window: (batch_size, 1, seq_len)
    time_window[have_no_power_surplus, 0, :] = 0.0

    # new_dynamic: (batch_size, input_size, seq_len)
    new_dynamic = torch.cat((time_window, access, memory_surplus, power_surplus, last_task, last_start_time), dim=1)
    return new_dynamic


def update_mask(dynamic):
    """
    Marks the visited city, so it can't be selected a second time
    mask: (batch_size, seq_len)
    dynamic: (batch_size, input_size, seq_len)
    chosen_idx: (batch_size,)
    """
    # dynamic: (batch_size, input_size, seq_len)
    # time_window: (batch_size, seq_len)
    time_window = dynamic[:, 0, :].clone()
    # time_mask: (batch_size, seq_len)
    time_mask = time_window.ne(0)

    # access: (batch_size, seq_len)
    access = dynamic[:, 1, :].clone()
    # access_mask: (batch_size, seq_len)
    access_mask = access.ne(0)

    # new_mask: (batch_size, seq_len)
    new_mask = torch.mul(time_mask, access_mask)

    # have_time_window: (batch_size, 1)
    have_time_window = new_mask.sum(1).gt(0).float()
    # have_no_time_window_index: (batch_size,)
    have_no_time_window_index = torch.sub(1, have_time_window.squeeze()).nonzero()

    new_mask[have_no_time_window_index, 0] = 1.
    # # # new_mask: (batch_size, seq_len)
    new_mask[have_no_time_window_index, 1:] = 0.

    if have_no_time_window_index.size(0) == time_window.size(0):
        new_mask[:, :] = 0.

    return new_mask.float()


if __name__ == '__main__':
    print('start_time:', start_time)
    print('end_time:', end_time)
    print('position:', position)
    print('require_time', require_time)
    print('revenue', revenue)
    print('memory_consume', memory_consume)
    print('power_consume', power_consume)

    print('static', static, static.size())
    print('dynamic', dynamic, dynamic.size())
