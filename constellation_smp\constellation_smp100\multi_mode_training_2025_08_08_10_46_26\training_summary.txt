🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_08_10_46_26
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_08_10_52_42
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: 12.0197, Loss: 132.8583, Revenue: 0.4350, LoadBalance: 0.4674, Tasks: [S0:746(44.0%), S1:241(14.2%), S2:709(41.8%)], ActorGrad: 60.6139, CriticGrad: 582.8978, Advantage: μ=1.679, σ=0.562, range=[0.61, 3.40]
Epoch 1, Batch 50/3125, loss: 126.971↓, reward: 14.224↑, critic_reward: 3.590, revenue_rate: 0.5475, distance: 10.4083, memory: 0.0710, power: 0.3357, lr: 0.000050, took: 353.334s
Batch 50: Reward: 15.9067, Loss: 150.6659, Revenue: 0.6153, LoadBalance: 0.0130, Tasks: [S0:597(25.9%), S1:42(1.8%), S2:1665(72.3%)], ActorGrad: 69.7505, CriticGrad: 136.8199, Advantage: μ=1.700, σ=0.493, range=[0.80, 3.00]
Epoch 1, Batch 100/3125, loss: 119.068↓, reward: 14.446↓, critic_reward: 4.286, revenue_rate: 0.5682, distance: 10.7627, memory: 0.0631, power: 0.3442, lr: 0.000050, took: 354.266s
Batch 100: Reward: 10.4863, Loss: 47.1152, Revenue: 0.4030, LoadBalance: 0.2529, Tasks: [S0:225(14.6%), S1:375(24.4%), S2:936(60.9%)], ActorGrad: 52.4012, CriticGrad: 72.9076, Advantage: μ=1.530, σ=0.900, range=[0.00, 3.64]
Epoch 1, Batch 150/3125, loss: 54.546↑, reward: 11.015↑, critic_reward: 4.489, revenue_rate: 0.4067, distance: 7.5330, memory: 0.0582, power: 0.2456, lr: 0.000050, took: 268.671s
Batch 150: Reward: 8.0961, Loss: 21.3666, Revenue: 0.3467, LoadBalance: 0.4718, Tasks: [S0:221(16.8%), S1:431(32.9%), S2:660(50.3%)], ActorGrad: 49.8999, CriticGrad: 58.3937, Advantage: μ=1.380, σ=1.123, range=[-1.12, 3.53]
Epoch 1, Batch 200/3125, loss: 37.079↑, reward: 9.764↑, critic_reward: 4.627, revenue_rate: 0.3662, distance: 6.6174, memory: 0.0485, power: 0.2157, lr: 0.000050, took: 236.465s
Batch 200: Reward: 8.5987, Loss: 19.4380, Revenue: 0.3385, LoadBalance: 0.6241, Tasks: [S0:395(31.7%), S1:299(24.0%), S2:554(44.4%)], ActorGrad: 44.9958, CriticGrad: 45.0949, Advantage: μ=1.573, σ=0.819, range=[0.14, 3.24]
Epoch 1, Batch 250/3125, loss: 28.266↑, reward: 9.125↑, critic_reward: 4.736, revenue_rate: 0.3420, distance: 6.0169, memory: 0.0410, power: 0.1980, lr: 0.000050, took: 218.640s
Batch 250: Reward: 9.0027, Loss: 22.7094, Revenue: 0.3428, LoadBalance: 0.6645, Tasks: [S0:431(33.7%), S1:531(41.5%), S2:318(24.8%)], ActorGrad: 55.5859, CriticGrad: 49.2523, Advantage: μ=1.509, σ=0.936, range=[-0.53, 3.42]
Epoch 1, Batch 300/3125, loss: 28.153↑, reward: 9.210↑, critic_reward: 4.897, revenue_rate: 0.3374, distance: 5.9574, memory: 0.0492, power: 0.1970, lr: 0.000050, took: 214.656s
Batch 300: Reward: 9.1892, Loss: 26.4402, Revenue: 0.3568, LoadBalance: 0.4809, Tasks: [S0:618(46.0%), S1:208(15.5%), S2:518(38.5%)], ActorGrad: 35.6928, CriticGrad: 52.6210, Advantage: μ=1.527, σ=0.904, range=[-0.22, 3.55]
Epoch 1, Batch 350/3125, loss: 66.442↑, reward: 12.303↑, critic_reward: 4.987, revenue_rate: 0.4591, distance: 8.6726, memory: 0.0636, power: 0.2802, lr: 0.000050, took: 305.628s
Batch 350: Reward: 11.4011, Loss: 50.2253, Revenue: 0.4545, LoadBalance: 0.0517, Tasks: [S0:340(18.6%), S1:174(9.5%), S2:1310(71.8%)], ActorGrad: 47.0383, CriticGrad: 78.1263, Advantage: μ=1.564, σ=0.836, range=[0.19, 4.27]
Epoch 1, Batch 400/3125, loss: 74.487↑, reward: 12.891↑, critic_reward: 5.069, revenue_rate: 0.4823, distance: 9.1201, memory: 0.0478, power: 0.2951, lr: 0.000050, took: 323.801s
Batch 400: Reward: 15.2455, Loss: 119.9679, Revenue: 0.5065, LoadBalance: 0.2190, Tasks: [S0:1383(62.6%), S1:273(12.4%), S2:552(25.0%)], ActorGrad: 82.6307, CriticGrad: 119.3543, Advantage: μ=1.618, σ=0.724, range=[0.61, 4.05]
Epoch 1, Batch 450/3125, loss: 57.098↓, reward: 11.816↓, critic_reward: 5.148, revenue_rate: 0.4380, distance: 8.2677, memory: 0.0563, power: 0.2673, lr: 0.000050, took: 294.315s
Batch 450: Reward: 11.9640, Loss: 54.5126, Revenue: 0.4434, LoadBalance: 0.3990, Tasks: [S0:765(46.0%), S1:186(11.2%), S2:713(42.8%)], ActorGrad: 38.0250, CriticGrad: 80.4069, Advantage: μ=1.640, σ=0.670, range=[0.17, 2.92]
Epoch 1, Batch 500/3125, loss: 46.958↓, reward: 11.174↓, critic_reward: 5.293, revenue_rate: 0.4163, distance: 7.7053, memory: 0.0519, power: 0.2501, lr: 0.000050, took: 274.372s
Batch 500: Reward: 10.0887, Loss: 32.7830, Revenue: 0.4074, LoadBalance: 0.4090, Tasks: [S0:807(52.5%), S1:236(15.4%), S2:493(32.1%)], ActorGrad: 29.8559, CriticGrad: 54.5459, Advantage: μ=1.473, σ=0.992, range=[-1.05, 3.22]
Epoch 1, Batch 550/3125, loss: 24.313↓, reward: 9.062↓, critic_reward: 5.385, revenue_rate: 0.3391, distance: 6.0213, memory: 0.0523, power: 0.1987, lr: 0.000050, took: 214.702s
Batch 550: Reward: 9.4100, Loss: 23.0723, Revenue: 0.3280, LoadBalance: 0.7280, Tasks: [S0:381(31.3%), S1:466(38.3%), S2:369(30.3%)], ActorGrad: 28.0205, CriticGrad: 48.0573, Advantage: μ=1.563, σ=0.840, range=[-0.44, 3.16]
Epoch 1, Batch 600/3125, loss: 18.537↓, reward: 8.557↓, critic_reward: 5.428, revenue_rate: 0.3177, distance: 5.5783, memory: 0.0428, power: 0.1834, lr: 0.000050, took: 202.704s
Batch 600: Reward: 7.9756, Loss: 9.8234, Revenue: 0.3155, LoadBalance: 0.6181, Tasks: [S0:270(22.8%), S1:432(36.5%), S2:482(40.7%)], ActorGrad: 42.0189, CriticGrad: 28.7663, Advantage: μ=1.404, σ=1.091, range=[-0.25, 3.65]
Epoch 1, Batch 650/3125, loss: 17.133↑, reward: 8.501↑, critic_reward: 5.537, revenue_rate: 0.3158, distance: 5.5210, memory: 0.0474, power: 0.1824, lr: 0.000050, took: 197.260s
Batch 650: Reward: 8.9217, Loss: 18.7015, Revenue: 0.3359, LoadBalance: 0.6975, Tasks: [S0:514(39.2%), S1:342(26.1%), S2:456(34.8%)], ActorGrad: 28.8712, CriticGrad: 38.3539, Advantage: μ=1.374, σ=1.130, range=[-0.67, 3.71]
Epoch 1, Batch 700/3125, loss: 16.618↓, reward: 8.533↓, critic_reward: 5.620, revenue_rate: 0.3160, distance: 5.5068, memory: 0.0491, power: 0.1830, lr: 0.000050, took: 197.425s
Batch 700: Reward: 9.6050, Loss: 25.1510, Revenue: 0.3408, LoadBalance: 0.7502, Tasks: [S0:400(32.9%), S1:430(35.4%), S2:386(31.7%)], ActorGrad: 28.4361, CriticGrad: 45.7551, Advantage: μ=1.400, σ=1.096, range=[-1.03, 3.32]
Epoch 1, Batch 750/3125, loss: 19.390↓, reward: 8.935↓, critic_reward: 5.670, revenue_rate: 0.3300, distance: 5.7392, memory: 0.0412, power: 0.1908, lr: 0.000050, took: 208.498s
Batch 750: Reward: 9.1894, Loss: 19.7469, Revenue: 0.3143, LoadBalance: 0.7354, Tasks: [S0:409(36.5%), S1:364(32.5%), S2:347(31.0%)], ActorGrad: 31.6838, CriticGrad: 38.3476, Advantage: μ=1.279, σ=1.239, range=[-1.26, 3.62]
Epoch 1, Batch 800/3125, loss: 19.369↓, reward: 8.921↓, critic_reward: 5.751, revenue_rate: 0.3288, distance: 5.7780, memory: 0.0431, power: 0.1902, lr: 0.000050, took: 208.270s
Batch 800: Reward: 8.7632, Loss: 19.1746, Revenue: 0.3115, LoadBalance: 0.7120, Tasks: [S0:402(36.9%), S1:385(35.4%), S2:301(27.7%)], ActorGrad: 33.4776, CriticGrad: 34.3091, Advantage: μ=1.175, σ=1.342, range=[-1.04, 3.76]
Epoch 1, Batch 850/3125, loss: 16.834↑, reward: 8.672↑, critic_reward: 5.906, revenue_rate: 0.3202, distance: 5.5699, memory: 0.0447, power: 0.1829, lr: 0.000050, took: 198.100s
Batch 850: Reward: 9.7488, Loss: 25.6147, Revenue: 0.3460, LoadBalance: 0.6425, Tasks: [S0:542(42.3%), S1:335(26.2%), S2:403(31.5%)], ActorGrad: 31.8839, CriticGrad: 44.6955, Advantage: μ=1.407, σ=1.087, range=[-0.52, 3.36]
Epoch 1, Batch 900/3125, loss: 17.421↓, reward: 8.813↓, critic_reward: 5.947, revenue_rate: 0.3241, distance: 5.6171, memory: 0.0389, power: 0.1862, lr: 0.000050, took: 200.918s
Batch 900: Reward: 8.5427, Loss: 10.7976, Revenue: 0.3348, LoadBalance: 0.5847, Tasks: [S0:352(28.2%), S1:593(47.5%), S2:303(24.3%)], ActorGrad: 28.0320, CriticGrad: 28.0743, Advantage: μ=1.320, σ=1.195, range=[-0.95, 3.74]
Epoch 1, Batch 950/3125, loss: 14.747↑, reward: 8.532↑, critic_reward: 6.051, revenue_rate: 0.3203, distance: 5.6472, memory: 0.0536, power: 0.1864, lr: 0.000050, took: 203.856s
Batch 950: Reward: 7.3580, Loss: 7.6120, Revenue: 0.2968, LoadBalance: 0.6507, Tasks: [S0:448(40.0%), S1:275(24.6%), S2:397(35.4%)], ActorGrad: 23.1904, CriticGrad: 18.7827, Advantage: μ=1.040, σ=1.452, range=[-1.82, 4.12]
Epoch 1, Batch 1000/3125, loss: 15.996↑, reward: 8.793↑, critic_reward: 6.119, revenue_rate: 0.3292, distance: 5.7766, memory: 0.0420, power: 0.1896, lr: 0.000050, took: 207.507s
Batch 1000: Reward: 9.7238, Loss: 27.1288, Revenue: 0.3365, LoadBalance: 0.7645, Tasks: [S0:420(32.8%), S1:434(33.9%), S2:426(33.3%)], ActorGrad: 24.4034, CriticGrad: 43.6109, Advantage: μ=1.324, σ=1.190, range=[-1.42, 3.97]
Epoch 1, Batch 1050/3125, loss: 14.529↓, reward: 8.602↓, critic_reward: 6.146, revenue_rate: 0.3175, distance: 5.5031, memory: 0.0465, power: 0.1824, lr: 0.000050, took: 198.804s
Batch 1050: Reward: 7.8534, Loss: 7.8792, Revenue: 0.2968, LoadBalance: 0.6823, Tasks: [S0:295(27.1%), S1:359(33.0%), S2:434(39.9%)], ActorGrad: 28.0229, CriticGrad: 20.8665, Advantage: μ=1.134, σ=1.378, range=[-1.82, 3.86]
Epoch 1, Batch 1100/3125, loss: 14.688↑, reward: 8.625↓, critic_reward: 6.283, revenue_rate: 0.3224, distance: 5.6468, memory: 0.0436, power: 0.1841, lr: 0.000050, took: 199.523s
Batch 1100: Reward: 8.2015, Loss: 10.6424, Revenue: 0.3147, LoadBalance: 0.7452, Tasks: [S0:433(36.6%), S1:384(32.4%), S2:367(31.0%)], ActorGrad: 35.6691, CriticGrad: 24.3836, Advantage: μ=1.047, σ=1.447, range=[-1.92, 5.72]
Epoch 1, Batch 1150/3125, loss: 13.200↓, reward: 8.483↓, critic_reward: 6.335, revenue_rate: 0.3125, distance: 5.4340, memory: 0.0423, power: 0.1801, lr: 0.000050, took: 194.361s
Batch 1150: Reward: 9.2284, Loss: 15.1687, Revenue: 0.3356, LoadBalance: 0.7734, Tasks: [S0:467(36.5%), S1:424(33.1%), S2:389(30.4%)], ActorGrad: 32.9153, CriticGrad: 33.1702, Advantage: μ=1.244, σ=1.276, range=[-1.43, 4.18]
Epoch 1, Batch 1200/3125, loss: 14.448↓, reward: 8.649↓, critic_reward: 6.415, revenue_rate: 0.3177, distance: 5.4855, memory: 0.0351, power: 0.1820, lr: 0.000050, took: 199.073s
Batch 1200: Reward: 7.2228, Loss: 7.5783, Revenue: 0.2913, LoadBalance: 0.6953, Tasks: [S0:323(29.7%), S1:324(29.8%), S2:441(40.5%)], ActorGrad: 27.1749, CriticGrad: 8.6260, Advantage: μ=0.395, σ=1.751, range=[-2.53, 5.41]
Epoch 1, Batch 1250/3125, loss: 15.244↑, reward: 8.785↑, critic_reward: 6.499, revenue_rate: 0.3227, distance: 5.6100, memory: 0.0456, power: 0.1864, lr: 0.000050, took: 208.568s
Batch 1250: Reward: 6.8591, Loss: 7.4015, Revenue: 0.2667, LoadBalance: 0.7638, Tasks: [S0:323(32.6%), S1:349(35.2%), S2:320(32.3%)], ActorGrad: 29.2451, CriticGrad: 9.0390, Advantage: μ=0.395, σ=1.751, range=[-2.20, 4.20]
Epoch 1, Batch 1300/3125, loss: 16.314↓, reward: 9.013↓, critic_reward: 6.592, revenue_rate: 0.3331, distance: 5.8920, memory: 0.0405, power: 0.1935, lr: 0.000050, took: 215.673s
Batch 1300: Reward: 8.7234, Loss: 10.9702, Revenue: 0.3350, LoadBalance: 0.5880, Tasks: [S0:468(39.5%), S1:271(22.9%), S2:445(37.6%)], ActorGrad: 20.7881, CriticGrad: 24.5157, Advantage: μ=1.071, σ=1.429, range=[-1.91, 5.53]
Epoch 1, Batch 1350/3125, loss: 14.037↑, reward: 8.915↑, critic_reward: 6.643, revenue_rate: 0.3323, distance: 5.8053, memory: 0.0428, power: 0.1916, lr: 0.000050, took: 203.455s
Batch 1350: Reward: 10.0718, Loss: 18.7762, Revenue: 0.3523, LoadBalance: 0.6682, Tasks: [S0:534(40.7%), S1:360(27.4%), S2:418(31.9%)], ActorGrad: 20.4899, CriticGrad: 42.1760, Advantage: μ=1.480, σ=0.982, range=[-0.34, 3.32]
Epoch 1, Batch 1400/3125, loss: 12.217↓, reward: 8.651↓, critic_reward: 6.724, revenue_rate: 0.3200, distance: 5.5611, memory: 0.0370, power: 0.1824, lr: 0.000050, took: 201.820s
Batch 1400: Reward: 7.4673, Loss: 5.9863, Revenue: 0.3083, LoadBalance: 0.6734, Tasks: [S0:353(30.6%), S1:306(26.6%), S2:493(42.8%)], ActorGrad: 26.3957, CriticGrad: 7.0967, Advantage: μ=0.350, σ=1.760, range=[-2.70, 4.99]
Epoch 1, Batch 1450/3125, loss: 13.863↑, reward: 8.746↑, critic_reward: 6.830, revenue_rate: 0.3229, distance: 5.6361, memory: 0.0377, power: 0.1852, lr: 0.000050, took: 202.002s
Batch 1450: Reward: 7.8235, Loss: 8.6961, Revenue: 0.2899, LoadBalance: 0.7496, Tasks: [S0:317(29.1%), S1:386(35.5%), S2:385(35.4%)], ActorGrad: 21.2377, CriticGrad: 10.8885, Advantage: μ=0.588, σ=1.694, range=[-3.49, 4.19]
Epoch 1, Batch 1500/3125, loss: 10.713↑, reward: 8.406↑, critic_reward: 6.885, revenue_rate: 0.3087, distance: 5.3283, memory: 0.0387, power: 0.1771, lr: 0.000050, took: 193.056s
Batch 1500: Reward: 9.7883, Loss: 19.5194, Revenue: 0.3595, LoadBalance: 0.7415, Tasks: [S0:479(34.8%), S1:447(32.5%), S2:450(32.7%)], ActorGrad: 36.3795, CriticGrad: 36.0954, Advantage: μ=1.178, σ=1.339, range=[-1.21, 4.06]
Epoch 1, Batch 1550/3125, loss: 12.305↑, reward: 8.716↑, critic_reward: 6.937, revenue_rate: 0.3204, distance: 5.5932, memory: 0.0441, power: 0.1852, lr: 0.000050, took: 200.537s
Batch 1550: Reward: 8.5636, Loss: 13.2118, Revenue: 0.3294, LoadBalance: 0.7327, Tasks: [S0:405(33.3%), S1:376(30.9%), S2:435(35.8%)], ActorGrad: 31.2269, CriticGrad: 20.0629, Advantage: μ=0.744, σ=1.629, range=[-2.68, 4.27]
Epoch 1, Batch 1600/3125, loss: 12.304↓, reward: 8.816↑, critic_reward: 7.066, revenue_rate: 0.3239, distance: 5.5902, memory: 0.0340, power: 0.1836, lr: 0.000050, took: 201.813s
Batch 1600: Reward: 8.2006, Loss: 5.3447, Revenue: 0.3080, LoadBalance: 0.7399, Tasks: [S0:415(35.1%), S1:390(32.9%), S2:379(32.0%)], ActorGrad: 34.5407, CriticGrad: 12.8867, Advantage: μ=0.796, σ=1.603, range=[-2.95, 3.88]
Epoch 1, Batch 1650/3125, loss: 12.833↑, reward: 8.891↑, critic_reward: 7.067, revenue_rate: 0.3324, distance: 5.8435, memory: 0.0443, power: 0.1930, lr: 0.000050, took: 212.042s
Batch 1650: Reward: 8.3011, Loss: 6.6757, Revenue: 0.3264, LoadBalance: 0.6411, Tasks: [S0:440(37.2%), S1:452(38.2%), S2:292(24.7%)], ActorGrad: 17.9235, CriticGrad: 14.9363, Advantage: μ=0.772, σ=1.616, range=[-1.70, 5.69]
Epoch 1, Batch 1700/3125, loss: 11.654↑, reward: 8.979↑, critic_reward: 7.209, revenue_rate: 0.3318, distance: 5.8457, memory: 0.0427, power: 0.1911, lr: 0.000050, took: 210.665s
Batch 1700: Reward: 6.7853, Loss: 4.7838, Revenue: 0.2829, LoadBalance: 0.5873, Tasks: [S0:242(22.2%), S1:342(31.4%), S2:504(46.3%)], ActorGrad: 30.1779, CriticGrad: 7.6495, Advantage: μ=-0.326, σ=1.765, range=[-3.20, 4.58]
Epoch 1, Batch 1750/3125, loss: 10.756↑, reward: 8.569↑, critic_reward: 7.242, revenue_rate: 0.3202, distance: 5.6188, memory: 0.0500, power: 0.1850, lr: 0.000050, took: 200.403s
Batch 1750: Reward: 9.3960, Loss: 12.0361, Revenue: 0.3510, LoadBalance: 0.7414, Tasks: [S0:381(29.8%), S1:452(35.3%), S2:447(34.9%)], ActorGrad: 21.6603, CriticGrad: 28.1122, Advantage: μ=1.109, σ=1.399, range=[-2.01, 3.94]
Epoch 1, Batch 1800/3125, loss: 12.617↓, reward: 8.867↓, critic_reward: 7.294, revenue_rate: 0.3310, distance: 5.7595, memory: 0.0336, power: 0.1891, lr: 0.000050, took: 205.230s
Batch 1800: Reward: 8.3738, Loss: 9.2932, Revenue: 0.2994, LoadBalance: 0.6933, Tasks: [S0:276(26.1%), S1:382(36.2%), S2:398(37.7%)], ActorGrad: 21.6876, CriticGrad: 10.1607, Advantage: μ=0.568, σ=1.701, range=[-2.35, 4.22]
Epoch 1, Batch 1850/3125, loss: 11.024↑, reward: 8.635↑, critic_reward: 7.313, revenue_rate: 0.3187, distance: 5.5466, memory: 0.0354, power: 0.1835, lr: 0.000050, took: 199.799s
Batch 1850: Reward: 7.7436, Loss: 6.4748, Revenue: 0.3036, LoadBalance: 0.7292, Tasks: [S0:389(34.7%), S1:416(37.1%), S2:315(28.1%)], ActorGrad: 21.2697, CriticGrad: 5.5502, Advantage: μ=0.252, σ=1.778, range=[-3.04, 3.33]
Epoch 1, Batch 1900/3125, loss: 9.483↑, reward: 8.459↑, critic_reward: 7.442, revenue_rate: 0.3124, distance: 5.3791, memory: 0.0428, power: 0.1777, lr: 0.000050, took: 193.154s
Batch 1900: Reward: 10.3775, Loss: 14.6685, Revenue: 0.3557, LoadBalance: 0.7664, Tasks: [S0:386(29.4%), S1:493(37.6%), S2:433(33.0%)], ActorGrad: 20.8561, CriticGrad: 37.0556, Advantage: μ=1.364, σ=1.143, range=[-0.62, 3.87]
Epoch 1, Batch 1950/3125, loss: 11.403↓, reward: 8.920↓, critic_reward: 7.484, revenue_rate: 0.3251, distance: 5.6712, memory: 0.0379, power: 0.1875, lr: 0.000050, took: 205.232s
Batch 1950: Reward: 10.0574, Loss: 14.2217, Revenue: 0.3501, LoadBalance: 0.7079, Tasks: [S0:516(36.6%), S1:403(28.6%), S2:489(34.7%)], ActorGrad: 34.4960, CriticGrad: 27.5296, Advantage: μ=1.040, σ=1.452, range=[-2.31, 4.38]
Epoch 1, Batch 2000/3125, loss: 13.401↓, reward: 9.381↓, critic_reward: 7.589, revenue_rate: 0.3462, distance: 6.0997, memory: 0.0370, power: 0.2004, lr: 0.000050, took: 218.693s
Batch 2000: Reward: 8.3800, Loss: 8.0953, Revenue: 0.3214, LoadBalance: 0.6090, Tasks: [S0:539(44.3%), S1:308(25.3%), S2:369(30.3%)], ActorGrad: 16.9240, CriticGrad: 11.5362, Advantage: μ=0.561, σ=1.703, range=[-2.64, 3.62]
Epoch 1, Batch 2050/3125, loss: 11.611↓, reward: 8.998↓, critic_reward: 7.586, revenue_rate: 0.3296, distance: 5.7797, memory: 0.0319, power: 0.1901, lr: 0.000050, took: 208.819s
Batch 2050: Reward: 9.5617, Loss: 9.7976, Revenue: 0.3400, LoadBalance: 0.6294, Tasks: [S0:282(22.0%), S1:524(40.9%), S2:474(37.0%)], ActorGrad: 40.3362, CriticGrad: 22.0637, Advantage: μ=1.028, σ=1.461, range=[-1.70, 4.34]
Epoch 1, Batch 2100/3125, loss: 11.317↓, reward: 9.139↓, critic_reward: 7.719, revenue_rate: 0.3263, distance: 5.5934, memory: 0.0303, power: 0.1870, lr: 0.000050, took: 203.416s
Batch 2100: Reward: 7.9878, Loss: 4.3804, Revenue: 0.3050, LoadBalance: 0.7401, Tasks: [S0:334(30.7%), S1:363(33.4%), S2:391(35.9%)], ActorGrad: 22.8465, CriticGrad: 3.8787, Advantage: μ=0.094, σ=1.794, range=[-4.10, 3.41]
Epoch 1, Batch 2150/3125, loss: 14.542↑, reward: 9.976↑, critic_reward: 7.769, revenue_rate: 0.3575, distance: 6.2605, memory: 0.0147, power: 0.2063, lr: 0.000050, took: 225.314s
Batch 2150: Reward: 8.9137, Loss: 10.7268, Revenue: 0.3327, LoadBalance: 0.6925, Tasks: [S0:477(40.3%), S1:305(25.8%), S2:402(34.0%)], ActorGrad: 26.1036, CriticGrad: 13.2328, Advantage: μ=0.516, σ=1.718, range=[-2.24, 3.50]
Epoch 1, Batch 2200/3125, loss: 18.245↑, reward: 10.492↑, critic_reward: 7.853, revenue_rate: 0.3663, distance: 6.3378, memory: -0.0159, power: 0.2069, lr: 0.000050, took: 225.326s
Batch 2200: Reward: 9.3190, Loss: 8.3902, Revenue: 0.3420, LoadBalance: 0.6644, Tasks: [S0:307(26.6%), S1:384(33.3%), S2:461(40.0%)], ActorGrad: 36.8394, CriticGrad: 16.9898, Advantage: μ=0.877, σ=1.560, range=[-1.90, 5.45]
Epoch 1, Batch 2250/3125, loss: 28.392↑, reward: 11.958↓, critic_reward: 7.975, revenue_rate: 0.3986, distance: 6.8295, memory: -0.0887, power: 0.2240, lr: 0.000050, took: 245.135s
Batch 2250: Reward: 9.3460, Loss: 14.3451, Revenue: 0.3456, LoadBalance: 0.6443, Tasks: [S0:314(23.4%), S1:577(42.9%), S2:453(33.7%)], ActorGrad: 42.4591, CriticGrad: 16.9593, Advantage: μ=0.715, σ=1.643, range=[-1.98, 4.70]
Epoch 1, Batch 2300/3125, loss: 25.703↑, reward: 11.428↑, critic_reward: 8.025, revenue_rate: 0.4309, distance: 7.9806, memory: 0.0388, power: 0.2598, lr: 0.000050, took: 283.754s
Batch 2300: Reward: 14.0282, Loss: 51.8072, Revenue: 0.4976, LoadBalance: 0.3397, Tasks: [S0:681(35.5%), S1:209(10.9%), S2:1030(53.6%)], ActorGrad: 34.5818, CriticGrad: 76.1427, Advantage: μ=1.491, σ=0.964, range=[-0.07, 3.93]
Epoch 1, Batch 2350/3125, loss: 44.675↓, reward: 13.611↓, critic_reward: 8.112, revenue_rate: 0.4634, distance: 8.3850, memory: -0.0530, power: 0.2713, lr: 0.000050, took: 298.550s
Batch 2350: Reward: 11.1831, Loss: 18.2989, Revenue: 0.3693, LoadBalance: 0.6690, Tasks: [S0:387(30.2%), S1:345(27.0%), S2:548(42.8%)], ActorGrad: 35.4272, CriticGrad: 38.5708, Advantage: μ=1.317, σ=1.199, range=[-0.80, 3.57]
Epoch 1, Batch 2400/3125, loss: 33.636↑, reward: 13.040↑, critic_reward: 8.254, revenue_rate: 0.3971, distance: 6.4979, memory: -0.1944, power: 0.2183, lr: 0.000050, took: 235.564s
Batch 2400: Reward: 12.5760, Loss: 30.8267, Revenue: 0.3697, LoadBalance: 0.7296, Tasks: [S0:439(34.3%), S1:473(37.0%), S2:368(28.7%)], ActorGrad: 36.3334, CriticGrad: 54.2813, Advantage: μ=1.404, σ=1.091, range=[-1.05, 3.09]
Epoch 1, Batch 2450/3125, loss: 42.365↑, reward: 13.897↑, critic_reward: 8.341, revenue_rate: 0.4039, distance: 6.6214, memory: -0.2444, power: 0.2222, lr: 0.000050, took: 241.530s
Batch 2450: Reward: 13.6378, Loss: 34.1615, Revenue: 0.3914, LoadBalance: 0.7260, Tasks: [S0:497(35.3%), S1:402(28.6%), S2:509(36.2%)], ActorGrad: 39.8296, CriticGrad: 68.5181, Advantage: μ=1.543, σ=0.876, range=[-0.33, 3.03]
Epoch 1, Batch 2500/3125, loss: 42.790↓, reward: 14.068↓, critic_reward: 8.399, revenue_rate: 0.4025, distance: 6.4692, memory: -0.2096, power: 0.2227, lr: 0.000050, took: 242.401s
Batch 2500: Reward: 12.8631, Loss: 22.9378, Revenue: 0.3843, LoadBalance: 0.7297, Tasks: [S0:535(39.8%), S1:429(31.9%), S2:380(28.3%)], ActorGrad: 41.7580, CriticGrad: 56.2692, Advantage: μ=1.581, σ=0.803, range=[-0.32, 2.65]
Epoch 1, Batch 2550/3125, loss: 47.662↑, reward: 14.513↑, critic_reward: 8.509, revenue_rate: 0.4070, distance: 6.6724, memory: -0.2051, power: 0.2299, lr: 0.000050, took: 248.563s
Batch 2550: Reward: 14.5687, Loss: 44.6484, Revenue: 0.3863, LoadBalance: 0.5619, Tasks: [S0:249(18.5%), S1:554(41.2%), S2:541(40.3%)], ActorGrad: 53.8863, CriticGrad: 78.5439, Advantage: μ=1.564, σ=0.837, range=[-0.05, 3.40]
Epoch 1, Batch 2600/3125, loss: 46.237↑, reward: 14.483↑, critic_reward: 8.575, revenue_rate: 0.4014, distance: 6.5986, memory: -0.3223, power: 0.2238, lr: 0.000050, took: 245.187s
Batch 2600: Reward: 15.2887, Loss: 55.8600, Revenue: 0.4130, LoadBalance: 0.7745, Tasks: [S0:562(37.4%), S1:451(30.0%), S2:491(32.6%)], ActorGrad: 40.1963, CriticGrad: 87.4252, Advantage: μ=1.554, σ=0.857, range=[0.02, 3.47]
Epoch 1, Batch 2650/3125, loss: 45.000↓, reward: 14.624↓, critic_reward: 8.656, revenue_rate: 0.3732, distance: 5.8381, memory: -0.3744, power: 0.2029, lr: 0.000050, took: 216.592s
Batch 2650: Reward: 14.8597, Loss: 41.0414, Revenue: 0.3810, LoadBalance: 0.6931, Tasks: [S0:542(41.3%), S1:391(29.8%), S2:379(28.9%)], ActorGrad: 52.7384, CriticGrad: 78.9570, Advantage: μ=1.598, σ=0.768, range=[0.26, 3.05]
Epoch 1, Batch 2700/3125, loss: 47.601↓, reward: 14.800↓, critic_reward: 8.766, revenue_rate: 0.3907, distance: 6.2565, memory: -0.2800, power: 0.2198, lr: 0.000050, took: 237.947s
Batch 2700: Reward: 13.4489, Loss: 27.7333, Revenue: 0.3621, LoadBalance: 0.6339, Tasks: [S0:544(42.5%), S1:292(22.8%), S2:444(34.7%)], ActorGrad: 42.1433, CriticGrad: 61.6665, Advantage: μ=1.544, σ=0.874, range=[-0.17, 4.16]
Epoch 1, Batch 2750/3125, loss: 44.866↑, reward: 14.770↑, critic_reward: 8.847, revenue_rate: 0.3856, distance: 6.1131, memory: -0.3475, power: 0.2131, lr: 0.000050, took: 228.590s
Batch 2750: Reward: 12.1775, Loss: 16.5438, Revenue: 0.3278, LoadBalance: 0.7035, Tasks: [S0:428(39.3%), S1:324(29.8%), S2:336(30.9%)], ActorGrad: 39.4138, CriticGrad: 45.3055, Advantage: μ=1.459, σ=1.014, range=[-1.36, 3.53]
Epoch 1, Batch 2800/3125, loss: 41.084↑, reward: 14.589↑, critic_reward: 8.921, revenue_rate: 0.3681, distance: 5.7254, memory: -0.3701, power: 0.1999, lr: 0.000050, took: 217.361s
Batch 2800: Reward: 13.7384, Loss: 30.4010, Revenue: 0.3502, LoadBalance: 0.6847, Tasks: [S0:452(38.2%), S1:426(36.0%), S2:306(25.8%)], ActorGrad: 29.5125, CriticGrad: 64.9741, Advantage: μ=1.541, σ=0.880, range=[-0.05, 3.33]
Epoch 1, Batch 2850/3125, loss: 45.027↑, reward: 15.020↑, critic_reward: 9.068, revenue_rate: 0.3695, distance: 5.8661, memory: -0.4186, power: 0.2029, lr: 0.000050, took: 224.788s
Batch 2850: Reward: 14.8039, Loss: 40.4873, Revenue: 0.3614, LoadBalance: 0.6834, Tasks: [S0:467(36.5%), S1:506(39.5%), S2:307(24.0%)], ActorGrad: 50.3540, CriticGrad: 77.1386, Advantage: μ=1.647, σ=0.652, range=[0.69, 2.81]
Epoch 1, Batch 2900/3125, loss: 47.001↓, reward: 15.180↓, critic_reward: 9.085, revenue_rate: 0.3727, distance: 5.8027, memory: -0.3745, power: 0.2080, lr: 0.000050, took: 235.680s
Batch 2900: Reward: 15.1321, Loss: 38.9654, Revenue: 0.3649, LoadBalance: 0.6480, Tasks: [S0:428(33.4%), S1:529(41.3%), S2:323(25.2%)], ActorGrad: 45.6095, CriticGrad: 79.3461, Advantage: μ=1.657, σ=0.625, range=[-0.00, 2.47]
Epoch 1, Batch 2950/3125, loss: 43.497↓, reward: 15.054↓, critic_reward: 9.209, revenue_rate: 0.3669, distance: 5.7196, memory: -0.3935, power: 0.2027, lr: 0.000050, took: 225.137s
Batch 2950: Reward: 14.7277, Loss: 38.2329, Revenue: 0.3529, LoadBalance: 0.6956, Tasks: [S0:406(34.3%), S1:323(27.3%), S2:455(38.4%)], ActorGrad: 49.6552, CriticGrad: 72.8406, Advantage: μ=1.597, σ=0.769, range=[0.04, 3.04]
Epoch 1, Batch 3000/3125, loss: 30.865↓, reward: 14.071↓, critic_reward: 9.279, revenue_rate: 0.3482, distance: 5.3426, memory: -0.4302, power: 0.1859, lr: 0.000050, took: 199.219s
Batch 3000: Reward: 14.7772, Loss: 38.2738, Revenue: 0.3900, LoadBalance: 0.7405, Tasks: [S0:429(30.5%), S1:470(33.4%), S2:509(36.2%)], ActorGrad: 43.7860, CriticGrad: 73.7330, Advantage: μ=1.563, σ=0.839, range=[-0.65, 3.03]
Epoch 1, Batch 3050/3125, loss: 31.994↓, reward: 14.364↓, critic_reward: 9.414, revenue_rate: 0.3503, distance: 5.3677, memory: -0.4250, power: 0.1889, lr: 0.000050, took: 197.783s
Batch 3050: Reward: 17.0809, Loss: 69.4299, Revenue: 0.3999, LoadBalance: 0.7317, Tasks: [S0:592(40.2%), S1:441(30.0%), S2:439(29.8%)], ActorGrad: 62.8911, CriticGrad: 102.1610, Advantage: μ=1.633, σ=0.688, range=[0.49, 3.16]
Epoch 1, Batch 3100/3125, loss: 41.123↓, reward: 15.108↓, critic_reward: 9.491, revenue_rate: 0.3591, distance: 5.5669, memory: -0.4170, power: 0.2005, lr: 0.000050, took: 215.895s
Batch 3100: Reward: 16.2365, Loss: 50.7291, Revenue: 0.3895, LoadBalance: 0.7273, Tasks: [S0:566(40.2%), S1:402(28.6%), S2:440(31.2%)], ActorGrad: 50.8692, CriticGrad: 90.6175, Advantage: μ=1.606, σ=0.749, range=[0.25, 3.21]

📊 Epoch 1 训练统计:
  平均奖励: 10.8597
  平均损失: 30.0317
  平均收益率: 0.3618
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 10/313, reward: 14.682, revenue_rate: 0.3215, efficiency: 0.1156, distance: 4.6648, memory: -0.4595, power: 0.1772
Test Batch 20/313, reward: 13.602, revenue_rate: 0.3097, efficiency: 0.1077, distance: 4.4501, memory: -0.3653, power: 0.1718
Test Batch 30/313, reward: 15.459, revenue_rate: 0.3588, efficiency: 0.1463, distance: 5.3987, memory: -0.3973, power: 0.1935
Test Batch 40/313, reward: 13.489, revenue_rate: 0.3093, efficiency: 0.1081, distance: 4.5192, memory: -0.3601, power: 0.1716
Test Batch 50/313, reward: 17.602, revenue_rate: 0.3997, efficiency: 0.1940, distance: 6.4572, memory: -0.4242, power: 0.2348
Test Batch 60/313, reward: 15.808, revenue_rate: 0.3488, efficiency: 0.1424, distance: 5.2472, memory: -0.4087, power: 0.1922
Test Batch 70/313, reward: 12.278, revenue_rate: 0.2949, efficiency: 0.0968, distance: 4.3587, memory: -0.3857, power: 0.1538
Test Batch 80/313, reward: 13.739, revenue_rate: 0.3133, efficiency: 0.1158, distance: 4.8730, memory: -0.3902, power: 0.1773
Test Batch 90/313, reward: 16.751, revenue_rate: 0.3909, efficiency: 0.1794, distance: 5.9873, memory: -0.3258, power: 0.2235
Test Batch 100/313, reward: 15.865, revenue_rate: 0.3569, efficiency: 0.1494, distance: 5.6981, memory: -0.4303, power: 0.2046
Test Batch 110/313, reward: 15.812, revenue_rate: 0.3898, efficiency: 0.1825, distance: 6.6019, memory: -0.3248, power: 0.2313
Test Batch 120/313, reward: 16.127, revenue_rate: 0.3560, efficiency: 0.1457, distance: 5.1670, memory: -0.4364, power: 0.2043
Test Batch 130/313, reward: 13.384, revenue_rate: 0.3063, efficiency: 0.1039, distance: 4.3657, memory: -0.3946, power: 0.1614
Test Batch 140/313, reward: 14.166, revenue_rate: 0.3135, efficiency: 0.1093, distance: 4.4694, memory: -0.3997, power: 0.1763
Test Batch 150/313, reward: 15.233, revenue_rate: 0.3364, efficiency: 0.1307, distance: 5.0381, memory: -0.3964, power: 0.1855
Test Batch 160/313, reward: 15.161, revenue_rate: 0.3524, efficiency: 0.1403, distance: 5.4399, memory: -0.4320, power: 0.1996
Test Batch 170/313, reward: 14.327, revenue_rate: 0.3434, efficiency: 0.1264, distance: 4.9682, memory: -0.3847, power: 0.1821
Test Batch 180/313, reward: 15.933, revenue_rate: 0.3558, efficiency: 0.1484, distance: 5.3377, memory: -0.3876, power: 0.2040
Test Batch 190/313, reward: 15.700, revenue_rate: 0.3652, efficiency: 0.1524, distance: 5.7748, memory: -0.3930, power: 0.2034
Test Batch 200/313, reward: 15.047, revenue_rate: 0.3396, efficiency: 0.1286, distance: 4.7102, memory: -0.3924, power: 0.1801
Test Batch 210/313, reward: 14.362, revenue_rate: 0.3486, efficiency: 0.1388, distance: 5.4515, memory: -0.3821, power: 0.1995
Test Batch 220/313, reward: 15.658, revenue_rate: 0.3591, efficiency: 0.1502, distance: 5.3660, memory: -0.3885, power: 0.2056
Test Batch 230/313, reward: 14.017, revenue_rate: 0.3156, efficiency: 0.1102, distance: 4.2571, memory: -0.4050, power: 0.1660
Test Batch 240/313, reward: 14.256, revenue_rate: 0.3322, efficiency: 0.1225, distance: 4.7771, memory: -0.3642, power: 0.1783
Test Batch 250/313, reward: 15.159, revenue_rate: 0.3330, efficiency: 0.1330, distance: 5.1259, memory: -0.3991, power: 0.2037
Test Batch 260/313, reward: 14.948, revenue_rate: 0.3403, efficiency: 0.1357, distance: 5.4166, memory: -0.4258, power: 0.1929
Test Batch 270/313, reward: 14.945, revenue_rate: 0.3431, efficiency: 0.1368, distance: 5.1440, memory: -0.3845, power: 0.1933
Test Batch 280/313, reward: 14.766, revenue_rate: 0.3327, efficiency: 0.1293, distance: 4.9510, memory: -0.3751, power: 0.1911
Test Batch 290/313, reward: 15.440, revenue_rate: 0.3453, efficiency: 0.1341, distance: 4.8860, memory: -0.4036, power: 0.1858
Test Batch 300/313, reward: 14.874, revenue_rate: 0.3351, efficiency: 0.1266, distance: 5.0473, memory: -0.3892, power: 0.1834
Test Batch 310/313, reward: 14.116, revenue_rate: 0.3224, efficiency: 0.1127, distance: 4.6631, memory: -0.4072, power: 0.1731
Test Batch 313/313, reward: 13.719, revenue_rate: 0.3031, efficiency: 0.0998, distance: 4.1101, memory: -0.4400, power: 0.1598
Test Summary - Avg reward: 14.725±2.797, revenue_rate: 0.3383±0.0385, efficiency: 0.1313, completion_rate: 0.3854, distance: 5.0617, memory: -0.3896, power: 0.1877
Load Balance - Avg balance score: 0.7454±0.1322
Task Distribution by Satellite:
  Satellite 1: 127650 tasks (33.87%)
  Satellite 2: 119786 tasks (31.79%)
  Satellite 3: 129396 tasks (34.34%)
✅ 验证完成 - Epoch 1, reward: 14.725, revenue_rate: 0.3383, distance: 5.0617, memory: -0.3896, power: 0.1877
  ⚠️ 欠拟合: 训练验证差距 = -3.8658
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_08_10_52_42 (验证集奖励: 14.7255)

开始训练 Epoch 2/3
Batch 0: Reward: 14.4534, Loss: 40.5732, Revenue: 0.3802, LoadBalance: 0.7394, Tasks: [S0:496(36.0%), S1:388(28.2%), S2:492(35.8%)], ActorGrad: 46.2114, CriticGrad: 65.1063, Advantage: μ=1.376, σ=1.127, range=[-1.66, 3.44]
Epoch 2, Batch 50/3125, loss: 29.606↓, reward: 14.234↓, critic_reward: 9.641, revenue_rate: 0.3355, distance: 5.0369, memory: -0.4072, power: 0.1835, lr: 0.000050, took: 202.895s
Batch 50: Reward: 14.8947, Loss: 38.8379, Revenue: 0.3555, LoadBalance: 0.7707, Tasks: [S0:409(32.8%), S1:441(35.3%), S2:398(31.9%)], ActorGrad: 37.4972, CriticGrad: 70.4282, Advantage: μ=1.588, σ=0.789, range=[0.56, 3.12]
Epoch 2, Batch 100/3125, loss: 31.672↑, reward: 14.558↑, critic_reward: 9.699, revenue_rate: 0.3537, distance: 5.4342, memory: -0.4385, power: 0.1901, lr: 0.000050, took: 207.735s
Batch 100: Reward: 14.9024, Loss: 34.1788, Revenue: 0.3561, LoadBalance: 0.6901, Tasks: [S0:317(25.4%), S1:409(32.8%), S2:522(41.8%)], ActorGrad: 54.4759, CriticGrad: 69.1527, Advantage: μ=1.522, σ=0.914, range=[0.01, 3.49]
Epoch 2, Batch 150/3125, loss: 37.880↑, reward: 15.196↑, critic_reward: 9.786, revenue_rate: 0.3637, distance: 5.5960, memory: -0.4080, power: 0.2005, lr: 0.000050, took: 218.458s
Batch 150: Reward: 15.1938, Loss: 42.5073, Revenue: 0.3424, LoadBalance: 0.7556, Tasks: [S0:425(35.0%), S1:356(29.3%), S2:435(35.8%)], ActorGrad: 43.6838, CriticGrad: 76.5865, Advantage: μ=1.553, σ=0.857, range=[-0.60, 2.77]
Epoch 2, Batch 200/3125, loss: 34.151↓, reward: 14.890↓, critic_reward: 9.883, revenue_rate: 0.3546, distance: 5.3848, memory: -0.4242, power: 0.1934, lr: 0.000050, took: 207.727s
Batch 200: Reward: 13.9673, Loss: 20.0860, Revenue: 0.3191, LoadBalance: 0.6759, Tasks: [S0:364(32.5%), S1:437(39.0%), S2:319(28.5%)], ActorGrad: 33.2035, CriticGrad: 52.2626, Advantage: μ=1.495, σ=0.959, range=[-0.10, 3.26]
Epoch 2, Batch 250/3125, loss: 30.296↑, reward: 14.670↑, critic_reward: 9.987, revenue_rate: 0.3437, distance: 5.1761, memory: -0.4138, power: 0.1875, lr: 0.000050, took: 207.692s
Batch 250: Reward: 12.8495, Loss: 16.9443, Revenue: 0.3219, LoadBalance: 0.7135, Tasks: [S0:320(28.6%), S1:412(36.8%), S2:388(34.6%)], ActorGrad: 44.6938, CriticGrad: 39.1474, Advantage: μ=1.227, σ=1.293, range=[-1.29, 4.36]
Epoch 2, Batch 300/3125, loss: 30.702↑, reward: 14.785↑, critic_reward: 10.112, revenue_rate: 0.3494, distance: 5.3699, memory: -0.4205, power: 0.1920, lr: 0.000050, took: 210.003s
Batch 300: Reward: 15.4859, Loss: 35.2402, Revenue: 0.3499, LoadBalance: 0.7347, Tasks: [S0:448(37.8%), S1:329(27.8%), S2:407(34.4%)], ActorGrad: 38.2898, CriticGrad: 72.7386, Advantage: μ=1.583, σ=0.799, range=[0.45, 4.23]
Epoch 2, Batch 350/3125, loss: 35.195↑, reward: 15.291↓, critic_reward: 10.102, revenue_rate: 0.3577, distance: 5.4703, memory: -0.4144, power: 0.1958, lr: 0.000050, took: 214.150s
Batch 350: Reward: 15.1143, Loss: 30.6297, Revenue: 0.3530, LoadBalance: 0.7017, Tasks: [S0:437(35.0%), S1:403(32.3%), S2:408(32.7%)], ActorGrad: 40.6774, CriticGrad: 66.6175, Advantage: μ=1.546, σ=0.871, range=[-1.27, 4.10]
Epoch 2, Batch 400/3125, loss: 35.429↓, reward: 15.395↓, critic_reward: 10.252, revenue_rate: 0.3666, distance: 5.5741, memory: -0.4093, power: 0.2002, lr: 0.000050, took: 220.335s
Batch 400: Reward: 15.3627, Loss: 32.9899, Revenue: 0.3853, LoadBalance: 0.7131, Tasks: [S0:393(27.9%), S1:576(40.9%), S2:439(31.2%)], ActorGrad: 52.5878, CriticGrad: 71.5634, Advantage: μ=1.516, σ=0.923, range=[-0.08, 3.15]
Epoch 2, Batch 450/3125, loss: 36.456↑, reward: 15.650↑, critic_reward: 10.378, revenue_rate: 0.3714, distance: 5.7104, memory: -0.4059, power: 0.2036, lr: 0.000050, took: 227.382s
Batch 450: Reward: 15.9856, Loss: 36.3423, Revenue: 0.3825, LoadBalance: 0.7303, Tasks: [S0:390(29.0%), S1:470(35.0%), S2:484(36.0%)], ActorGrad: 46.8037, CriticGrad: 77.8275, Advantage: μ=1.627, σ=0.701, range=[0.03, 3.47]
