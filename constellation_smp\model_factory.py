"""
星座任务规划模型工厂
统一管理不同类型的模型创建和配置
"""
import torch
from .gpn_constellation import GPNConstellation, ConstellationStateCritic
from .gpn_transformer import GPNTransformer, ConstellationTransformerStateCritic


class ModelFactory:
    """模型工厂类，用于创建和管理不同类型的模型"""
    
    SUPPORTED_MODELS = {
        'gpn': {
            'actor': GPNConstellation,
            'critic': ConstellationStateCritic,
            'description': '基于GPN的星座任务规划模型'
        },
        'gpn_transformer': {
            'actor': GPNTransformer,
            'critic': ConstellationTransformerStateCritic,
            'description': '基于Transformer的星座任务规划模型'
        }
    }
    
    @classmethod
    def create_actor(cls, model_type, args, train_data):
        """
        创建Actor模型
        
        Args:
            model_type: 模型类型 ('gpn' 或 'gpn_transformer')
            args: 超参数配置
            train_data: 训练数据集
            
        Returns:
            actor: Actor模型实例
        """
        if model_type not in cls.SUPPORTED_MODELS:
            raise ValueError(f"不支持的模型类型: {model_type}. 支持的类型: {list(cls.SUPPORTED_MODELS.keys())}")
        
        actor_class = cls.SUPPORTED_MODELS[model_type]['actor']
        
        if model_type == 'gpn':
            actor = actor_class(
                args.static_size,
                args.dynamic_size,
                args.hidden_size,
                args.num_satellites,
                args.rnn,
                args.num_layers,
                train_data.update_dynamic,
                train_data.update_mask,
                args.num_nodes,
                args.dropout,
                args.constellation_mode
            )
        elif model_type == 'gpn_transformer':
            actor = actor_class(
                args.static_size,
                args.dynamic_size,
                getattr(args, 'd_model', 256),
                getattr(args, 'n_head', 8),
                getattr(args, 'n_transformer_layers', 6),
                args.num_satellites,
                train_data.update_dynamic,
                train_data.update_mask,
                args.num_nodes,
                getattr(args, 'transformer_dropout', 0.1),
                args.constellation_mode
            )
        
        return actor
    
    @classmethod
    def create_critic(cls, model_type, args):
        """
        创建Critic模型
        
        Args:
            model_type: 模型类型 ('gpn' 或 'gpn_transformer')
            args: 超参数配置
            
        Returns:
            critic: Critic模型实例
        """
        if model_type not in cls.SUPPORTED_MODELS:
            raise ValueError(f"不支持的模型类型: {model_type}. 支持的类型: {list(cls.SUPPORTED_MODELS.keys())}")
        
        critic_class = cls.SUPPORTED_MODELS[model_type]['critic']
        
        if model_type == 'gpn':
            critic = critic_class(
                args.static_size,
                args.dynamic_size,
                args.hidden_size,
                args.num_satellites,
                args.constellation_mode
            )
        elif model_type == 'gpn_transformer':
            critic = critic_class(
                args.static_size,
                args.dynamic_size,
                getattr(args, 'd_model', 256),
                getattr(args, 'n_head', 8),
                getattr(args, 'n_transformer_layers', 6) // 2,  # 使用较少的层数
                args.num_satellites,
                args.constellation_mode
            )
        
        return critic
    
    @classmethod
    def get_model_info(cls, model_type):
        """
        获取模型信息
        
        Args:
            model_type: 模型类型
            
        Returns:
            dict: 模型信息
        """
        if model_type not in cls.SUPPORTED_MODELS:
            return None
        return cls.SUPPORTED_MODELS[model_type]
    
    @classmethod
    def list_supported_models(cls):
        """
        列出所有支持的模型类型
        
        Returns:
            list: 支持的模型类型列表
        """
        return list(cls.SUPPORTED_MODELS.keys())
    
    @classmethod
    def validate_model_config(cls, model_type, args):
        """
        验证模型配置
        
        Args:
            model_type: 模型类型
            args: 超参数配置
            
        Returns:
            bool: 配置是否有效
            list: 错误信息列表
        """
        errors = []
        
        if model_type not in cls.SUPPORTED_MODELS:
            errors.append(f"不支持的模型类型: {model_type}")
            return False, errors
        
        # 通用配置检查
        required_attrs = [
            'static_size', 'dynamic_size', 'num_satellites', 
            'num_nodes', 'constellation_mode'
        ]
        
        for attr in required_attrs:
            if not hasattr(args, attr):
                errors.append(f"缺少必需的配置参数: {attr}")
        
        # 模型特定配置检查
        if model_type == 'gpn':
            gpn_required = ['hidden_size', 'rnn', 'num_layers', 'dropout']
            for attr in gpn_required:
                if not hasattr(args, attr):
                    errors.append(f"GPN模型缺少必需的配置参数: {attr}")
        
        elif model_type == 'gpn_transformer':
            transformer_required = ['d_model', 'n_head', 'n_transformer_layers']
            for attr in transformer_required:
                if not hasattr(args, attr):
                    errors.append(f"Transformer模型缺少必需的配置参数: {attr}")
        
        return len(errors) == 0, errors


class ModelManager:
    """模型管理器，用于模型的保存、加载和管理"""
    
    def __init__(self, model_type, args, train_data=None):
        self.model_type = model_type
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 验证配置
        is_valid, errors = ModelFactory.validate_model_config(model_type, args)
        if not is_valid:
            raise ValueError(f"模型配置无效: {'; '.join(errors)}")
        
        # 创建模型
        if train_data is not None:
            self.actor = ModelFactory.create_actor(model_type, args, train_data).to(self.device)
        else:
            self.actor = None
        
        self.critic = ModelFactory.create_critic(model_type, args).to(self.device)
    
    def save_models(self, save_dir):
        """
        保存模型
        
        Args:
            save_dir: 保存目录
        """
        import os
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        if self.actor is not None:
            actor_path = os.path.join(save_dir, 'actor.pt')
            torch.save(self.actor.state_dict(), actor_path)
        
        critic_path = os.path.join(save_dir, 'critic.pt')
        torch.save(self.critic.state_dict(), critic_path)
        
        # 保存模型配置
        config_path = os.path.join(save_dir, 'model_config.pt')
        config = {
            'model_type': self.model_type,
            'args': self.args
        }
        torch.save(config, config_path)
    
    def load_models(self, load_dir):
        """
        加载模型
        
        Args:
            load_dir: 加载目录
        """
        import os
        
        if self.actor is not None:
            actor_path = os.path.join(load_dir, 'actor.pt')
            if os.path.exists(actor_path):
                self.actor.load_state_dict(torch.load(actor_path, map_location=self.device))
        
        critic_path = os.path.join(load_dir, 'critic.pt')
        if os.path.exists(critic_path):
            self.critic.load_state_dict(torch.load(critic_path, map_location=self.device))
    
    def get_model_summary(self):
        """
        获取模型摘要信息
        
        Returns:
            dict: 模型摘要
        """
        summary = {
            'model_type': self.model_type,
            'model_info': ModelFactory.get_model_info(self.model_type),
            'device': str(self.device)
        }
        
        if self.actor is not None:
            summary['actor_parameters'] = sum(p.numel() for p in self.actor.parameters())
            summary['actor_trainable_parameters'] = sum(p.numel() for p in self.actor.parameters() if p.requires_grad)
        
        summary['critic_parameters'] = sum(p.numel() for p in self.critic.parameters())
        summary['critic_trainable_parameters'] = sum(p.numel() for p in self.critic.parameters() if p.requires_grad)
        
        return summary
    
    def set_training_mode(self, training=True):
        """设置训练模式"""
        if self.actor is not None:
            self.actor.train(training)
        self.critic.train(training)
    
    def set_eval_mode(self):
        """设置评估模式"""
        self.set_training_mode(False)
