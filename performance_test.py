"""
星座任务规划模型性能测试脚本
测试不同模型在不同规模任务上的性能表现
"""
import os
import time
import torch
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
from constellation_smp.model_factory import ModelFactory, ModelManager
from constellation_smp.config_manager import ConfigManager
from hyperparameter import args
import argparse

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class PerformanceTester:
    """性能测试器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.results = {}
    
    def test_model_performance(self, model_type, test_sizes, num_satellites_list, constellation_modes):
        """
        测试模型在不同配置下的性能
        
        Args:
            model_type: 模型类型
            test_sizes: 测试任务规模列表
            num_satellites_list: 卫星数量列表
            constellation_modes: 星座模式列表
        """
        print(f"\n开始测试模型: {model_type}")
        print("=" * 50)
        
        for num_satellites in num_satellites_list:
            for constellation_mode in constellation_modes:
                for test_size in test_sizes:
                    print(f"\n测试配置: {num_satellites}颗卫星, {constellation_mode}模式, {test_size}个任务")
                    
                    # 更新配置
                    self.config_manager.model_config.model_type = model_type
                    self.config_manager.model_config.num_satellites = num_satellites
                    self.config_manager.model_config.constellation_mode = constellation_mode
                    self.config_manager.training_config.num_nodes = test_size
                    
                    # 运行测试
                    result = self._run_single_test()
                    
                    # 保存结果
                    key = f"{model_type}_{num_satellites}sats_{constellation_mode}_{test_size}nodes"
                    self.results[key] = result
                    
                    print(f"结果: 平均奖励={result['avg_reward']:.3f}, "
                          f"收益率={result['avg_revenue_rate']:.4f}, "
                          f"推理时间={result['inference_time']:.3f}s")
    
    def _run_single_test(self):
        """运行单个测试"""
        try:
            # 创建测试数据
            test_data = ConstellationSMPDataset(
                size=1000,  # 测试数据集大小
                num_nodes=self.config_manager.training_config.num_nodes,
                num_satellites=self.config_manager.model_config.num_satellites,
                memory_total=self.config_manager.data_config.memory_total,
                power_total=self.config_manager.data_config.power_total,
                task_sharing=self.config_manager.data_config.task_sharing,
                communication_delay=self.config_manager.data_config.communication_delay,
                satellite_distance=self.config_manager.data_config.satellite_distance,
                constellation_mode=self.config_manager.model_config.constellation_mode
            )
            
            # 创建模型
            model_manager = ModelManager(
                self.config_manager.model_config.model_type,
                self._create_args_from_config(),
                test_data
            )
            
            # 创建数据加载器
            test_loader = DataLoader(test_data, batch_size=32, shuffle=False, num_workers=0)
            
            # 运行测试
            start_time = time.time()
            avg_reward, avg_revenue_rate, avg_distance, avg_memory, avg_power = self._validate_model(
                test_loader, model_manager.actor, model_manager.critic
            )
            inference_time = time.time() - start_time
            
            return {
                'avg_reward': avg_reward,
                'avg_revenue_rate': avg_revenue_rate,
                'avg_distance': avg_distance,
                'avg_memory': avg_memory,
                'avg_power': avg_power,
                'inference_time': inference_time,
                'model_parameters': sum(p.numel() for p in model_manager.actor.parameters()),
                'success': True
            }
            
        except Exception as e:
            print(f"测试失败: {str(e)}")
            return {
                'avg_reward': 0,
                'avg_revenue_rate': 0,
                'avg_distance': 0,
                'avg_memory': 0,
                'avg_power': 0,
                'inference_time': float('inf'),
                'model_parameters': 0,
                'success': False,
                'error': str(e)
            }
    
    def _validate_model(self, data_loader, actor, critic):
        """验证模型性能"""
        actor.eval()
        critic.eval()
        
        total_reward = 0
        total_revenue_rate = 0
        total_distance = 0
        total_memory = 0
        total_power = 0
        total_samples = 0
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(data_loader):
                static, dynamic = batch[0], batch[1]
                static = static.to(device)
                dynamic = dynamic.to(device)
                batch_size = static.size(0)
                
                # 前向传播
                if hasattr(actor, 'forward'):
                    tour_indices, satellite_indices, tour_logp = actor(static, dynamic)
                else:
                    # 兼容旧版本接口
                    tour_indices, tour_logp = actor(static, dynamic)
                    satellite_indices = torch.zeros_like(tour_indices)
                
                # 计算奖励
                R = reward(static, tour_indices, satellite_indices, 
                          self.config_manager.model_config.constellation_mode)
                
                # 累计统计
                total_reward += R.sum().item()
                total_samples += batch_size
                
                # 计算其他指标（简化版本）
                total_revenue_rate += R.mean().item()
                total_distance += 0  # 简化
                total_memory += 0    # 简化
                total_power += 0     # 简化
                
                if batch_idx >= 10:  # 限制测试批次数量
                    break
        
        # 计算平均值
        avg_reward = total_reward / total_samples if total_samples > 0 else 0
        avg_revenue_rate = total_revenue_rate / min(len(data_loader), 10) if len(data_loader) > 0 else 0
        avg_distance = total_distance / total_samples if total_samples > 0 else 0
        avg_memory = total_memory / total_samples if total_samples > 0 else 0
        avg_power = total_power / total_samples if total_samples > 0 else 0
        
        return avg_reward, avg_revenue_rate, avg_distance, avg_memory, avg_power
    
    def _create_args_from_config(self):
        """从配置创建args对象"""
        class Args:
            pass
        
        args_obj = Args()
        
        # 添加模型配置
        for key, value in self.config_manager.get_model_params().items():
            setattr(args_obj, key, value)
        
        # 添加训练配置
        for key, value in self.config_manager.get_training_params().items():
            setattr(args_obj, key, value)
        
        # 添加数据配置
        for key, value in self.config_manager.get_data_params().items():
            setattr(args_obj, key, value)
        
        return args_obj
    
    def generate_performance_report(self, save_path=None):
        """生成性能报告"""
        if not self.results:
            print("没有测试结果可报告")
            return
        
        print("\n" + "=" * 60)
        print("性能测试报告")
        print("=" * 60)
        
        # 按模型类型分组
        model_types = set(key.split('_')[0] for key in self.results.keys())
        
        for model_type in model_types:
            print(f"\n模型类型: {model_type}")
            print("-" * 40)
            
            model_results = {k: v for k, v in self.results.items() if k.startswith(model_type)}
            
            for key, result in model_results.items():
                parts = key.split('_')
                config_str = f"{parts[1]} {parts[2]} {parts[3]}"
                
                if result['success']:
                    print(f"{config_str:20} | "
                          f"奖励: {result['avg_reward']:6.3f} | "
                          f"收益率: {result['avg_revenue_rate']:6.4f} | "
                          f"时间: {result['inference_time']:6.3f}s | "
                          f"参数: {result['model_parameters']:,}")
                else:
                    print(f"{config_str:20} | 失败: {result.get('error', '未知错误')}")
        
        # 保存报告
        if save_path:
            self._save_report_to_file(save_path)
    
    def _save_report_to_file(self, save_path):
        """保存报告到文件"""
        import json
        
        report_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'results': self.results,
            'summary': self._generate_summary()
        }
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n报告已保存到: {save_path}")
    
    def _generate_summary(self):
        """生成摘要统计"""
        successful_results = {k: v for k, v in self.results.items() if v['success']}
        
        if not successful_results:
            return {"message": "没有成功的测试结果"}
        
        rewards = [r['avg_reward'] for r in successful_results.values()]
        revenue_rates = [r['avg_revenue_rate'] for r in successful_results.values()]
        inference_times = [r['inference_time'] for r in successful_results.values()]
        
        return {
            'total_tests': len(self.results),
            'successful_tests': len(successful_results),
            'avg_reward_mean': np.mean(rewards),
            'avg_reward_std': np.std(rewards),
            'avg_revenue_rate_mean': np.mean(revenue_rates),
            'avg_revenue_rate_std': np.std(revenue_rates),
            'avg_inference_time_mean': np.mean(inference_times),
            'avg_inference_time_std': np.std(inference_times)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='星座任务规划模型性能测试')
    parser.add_argument('--models', nargs='+', default=['gpn', 'gpn_transformer'],
                       help='要测试的模型类型')
    parser.add_argument('--test_sizes', nargs='+', type=int, default=[50, 100, 200],
                       help='测试任务规模')
    parser.add_argument('--num_satellites', nargs='+', type=int, default=[3, 5],
                       help='卫星数量')
    parser.add_argument('--constellation_modes', nargs='+', 
                       default=['cooperative', 'competitive'],
                       help='星座模式')
    parser.add_argument('--output_dir', default='performance_results',
                       help='结果输出目录')
    
    test_args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(test_args.output_dir, exist_ok=True)
    
    # 创建配置管理器
    config_manager = ConfigManager()
    
    # 创建性能测试器
    tester = PerformanceTester(config_manager)
    
    # 运行测试
    for model_type in test_args.models:
        try:
            tester.test_model_performance(
                model_type,
                test_args.test_sizes,
                test_args.num_satellites,
                test_args.constellation_modes
            )
        except Exception as e:
            print(f"测试模型 {model_type} 时出错: {str(e)}")
    
    # 生成报告
    report_path = os.path.join(test_args.output_dir, 'performance_report.json')
    tester.generate_performance_report(report_path)


if __name__ == '__main__':
    main()
