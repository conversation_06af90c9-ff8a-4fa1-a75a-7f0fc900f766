import matplotlib.pyplot as plt
import numpy as np
import os
import re

# 配置中文字体支持
try:
    # 尝试设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
except:
    print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")

# 平滑曲线函数
def smooth_curve(points, factor=0.8):
    smoothed_points = []
    for point in points:
        if smoothed_points:
            previous = smoothed_points[-1]
            # 上一个节点*0.8+当前节点*0.2
            smoothed_points.append(previous * factor + point * (1 - factor))
        else:
            # 添加point
            smoothed_points.append(point)
    return smoothed_points

# 指定日志文件路径
log_files = [
    'constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_02_09_45_01/log.txt',
    'constellation_smp/constellation_smp100/constellation_gpnindrnn2025_07_02_10_00_49/log.txt'
]

# 存储不同模型的数据
models_data = {}

# 从日志文件中提取收益率数据
for log_file in log_files:
    if not os.path.exists(log_file):
        print(f"警告: 文件 {log_file} 不存在")
        continue
        
    # 从文件名提取模型名称
    model_name = log_file.split('/')[-2]
    
    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
        revenue_rates = []
        
        for line in lines:
            # 匹配包含revenue_rate的行
            if 'Batch' in line and 'revenue_rate:' in line:
                match = re.search(r'revenue_rate: (\d+\.\d+)', line)
                if match:
                    revenue_rate = float(match.group(1))
                    revenue_rates.append(revenue_rate)
        
        if revenue_rates:
            models_data[model_name] = revenue_rates
            print(f"从 {log_file} 中提取了 {len(revenue_rates)} 条收益率数据")
        else:
            print(f"未能从 {log_file} 中提取收益率数据")

# 绘制图表
if models_data:
    plt.figure(figsize=(12, 8))
    
    # 颜色列表
    colors = ['blue', 'green', 'red', 'purple', 'orange', 'cyan']
    
    # 绘制每个模型的收益率曲线
    for i, (model, revenue_rates) in enumerate(models_data.items()):
        color = colors[i % len(colors)]
        plt.plot(smooth_curve(revenue_rates), label=f"模型: {model}", color=color)
    
    # 设置图表标题和标签
    plt.title('训练过程中的收益率(Revenue Rate)变化', fontsize=16)
    plt.xlabel('训练步数', fontsize=14)
    plt.ylabel('收益率 (Revenue Rate)', fontsize=14)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend()
    
    # 保存图表
    plt.tight_layout()
    plt.savefig('revenue_rate_training.png', dpi=300)
    print(f"图表已保存为 revenue_rate_training.png")
    plt.show()
else:
    print("没有找到收益率数据，无法绘制图表") 