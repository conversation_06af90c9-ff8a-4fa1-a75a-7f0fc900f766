"""
Defines the main task for the SMPV2(Satellite mission planning).

The SMPV2 is defined by the following traits:
    1. Planning starts from the starting point, each task in the list is visited only once.
    2. Each task has revenue, and the goal is to maximize revenue.In order to unify the
       difference of different samples and maximize the revenue rate.
    3. There are time window constraints in task transfer, When there are no optional tasks that
       satisfy the time window constraints, return to the starting point.
    4. Each task consumes memory space, it stops acting when the memory is used up.
    5. Each task consumes electricity, it stops acting when the electricity is used up.

SMPDataset:
    static_data: (num_samples, static_size, seq_len)
        StaticData[:, 0, :]: task start time.
        StaticData[:, 1, :]: task longitudinal axis position.
        StaticData[:, 2, :]: task end time.
        StaticData[:, 3, :]: time required for observation tasks.
        StaticData[:, 4, :]: task revenue.
        StaticData[:, 5, :]: task memory consumption.
        StaticData[:, 6, :]: task power consumption.

    dynamic_data: (num_samples, dynamic_size, seq_len)
        DynamicData[:, 0, :]: task time windows mark. 1 -> 0.
        DynamicData[:, 1, :]: task access mark. 1 -> 0.
        DynamicData[:, 2, :]: memory surplus. decrement.
        DynamicData[:, 3, :]: power surplus. decrement.
        DynamicData[:, 4, :]: task_last.  Store the ordinal number of the previous node.
        DynamicData[:, 5, :]: start time of current task execution.

    e.g.: num_samples = 1e6.
          static_size = 7.
          dynamic_size = 6.
          seq_len = 20/50/100.

update_dynamic: To update dynamic element according to chosen_idx.

updata_mask: To update mask according to dynamic and chosen_idx.

reward: The reward function, objectives of optimization.

render: Draw out the results of the plan.
"""

import numpy as np
import matplotlib.pyplot as plt
import torch
from torch.utils.data import Dataset

HAVE_STATION = 1  # 是否含station

# SMP scene parameters
START_TIME_WIDTH = 6.0  # 一次过境总时间
POSITION_WIDTH = 0.5  # 侧摆角度范最大范围 [-0.25,0.25]

END_TIME_START = 0.2
END_TIME_END = 0.3  # 时间窗口长度 [0.2,0.3]

STABILIZATION_TIME = 0.005  # 姿态调整后的稳定时间
REQUIRE_TIME_START = 0.015
REQUIRE_TIME_END = 0.030  # 任务执行所需时间 [0.015,0.030]

REVENUE_START = 1
REVENUE_END = 9  # 收益[1,9]

MEMORY_CONSUME = 0.01  # 内存消耗
POWER_CONSUME = 0.01  # 能量消耗

# Satellite parameters
MOVING_TIME_RATE = 0.1  #
MOVING_CONSUME_POWER_RATE = 0.01

# Reward proportion parameters
REWARD_PROPORTION = 1
DISTANCE_PROPORTION = 0
POWER_PROPORTION = 0

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
if device == 'cuda':
    torch.backends.cudnn.enabled = False


class SMPDataset(Dataset):
    def __init__(self, size=50, num_samples=1000000, seed=None, memory_total=0.5, power_total=0.5):
        super(SMPDataset, self).__init__()
        self.memory_total = memory_total
        self.power_total = power_total
        if seed is None:
            seed = np.random.randint(123456789)
        np.random.seed(seed)
        torch.manual_seed(seed)

        static_size = (num_samples, 1, size)
        start_time = torch.from_numpy(np.random.uniform(0, START_TIME_WIDTH, size=static_size)).float()
        position = torch.from_numpy(
            np.random.uniform(POSITION_WIDTH / 2.0 * (-1), POSITION_WIDTH / 2.0, size=static_size)).float()
        end_time = torch.add(start_time, torch.from_numpy(
            np.random.uniform(END_TIME_START, END_TIME_END, size=static_size)).float())
        require_time = torch.from_numpy(
            np.random.uniform(REQUIRE_TIME_START, REQUIRE_TIME_END, size=static_size)).float()
        revenue = torch.from_numpy(np.random.randint(REVENUE_START, REVENUE_END, size=static_size) / 10).float()
        memory_consume = torch.from_numpy(np.random.uniform(0, MEMORY_CONSUME, size=static_size)).float()
        power_consume = torch.from_numpy(np.random.uniform(0, POWER_CONSUME, size=static_size)).float()

        # add earth station
        # earth_stations = torch.from_numpy(np.random.uniform(0, 1, (num_samples, 1, size))).float()
        # station_idx, _ = torch.max(earth_stations, dim=2)
        # station_idx = station_idx.expand(num_samples, size).unsqueeze(1)
        # station_idx = torch.sub(earth_stations, station_idx).eq(0).int()
        # revenue = torch.add(revenue, 3 * station_idx)
        # revenue = torch.clamp(revenue, 0.1, 3)
        # memory_given = self.memory_total * station_idx
        # memory_consume = torch.sub(memory_consume, memory_given)

        # have station
        # self.static = torch.cat((start_time, position, end_time, require_time, revenue,
        #                          memory_consume, power_consume, station_idx), dim=1)  # torch.cat() 拼接 dim=1 按第一维拼接

        # don't have station
        if HAVE_STATION == 1:
            earth_stations = torch.from_numpy(np.random.uniform(0, 1, (num_samples, 1, size))).float()
            station_idx, _ = torch.max(earth_stations, dim=2)
            station_idx = station_idx.expand(num_samples, size).unsqueeze(1)
            station_idx = torch.sub(earth_stations, station_idx).eq(0).int()
            revenue = torch.add(revenue, 3 * station_idx)
            revenue = torch.clamp(revenue, 0.1, 3)
            memory_given = self.memory_total * station_idx
            memory_consume = torch.sub(memory_consume, memory_given)
            self.static = torch.cat((start_time, position, end_time, require_time, revenue,
                                     memory_consume, power_consume, station_idx), dim=1)  # torch.cat() 拼接 dim=1 按第一维拼接

        if HAVE_STATION == 0:
            self.static = torch.cat((start_time, position, end_time, require_time, revenue,
                                     memory_consume, power_consume), dim=1)  # torch.cat() 拼接 dim=1 按第一维拼接
        self.static[:, :, 0] = 0.

        dynamic_size = (num_samples, 1, size)
        time_window = torch.from_numpy(np.ones(dynamic_size)).float()
        access = torch.from_numpy(np.ones(dynamic_size)).float()
        memory_surplus = torch.from_numpy(np.ones(dynamic_size) * self.memory_total).float()
        power_surplus = torch.from_numpy(np.ones(dynamic_size) * self.power_total).float()
        last_task = torch.from_numpy(np.zeros(dynamic_size)).float()
        start_execution = torch.from_numpy(np.zeros(dynamic_size)).float()
        self.dynamic = torch.cat((time_window, access, memory_surplus,
                                  power_surplus, last_task, start_execution), dim=1)

        self.num_nodes = size
        self.size = num_samples

    def __len__(self):
        return self.size

    def __getitem__(self, idx):
        return self.static[idx], self.dynamic[idx], []

    def update_dynamic(self, static, dynamic, chosen_idx):
        """
        static: (batch_size, input_size, seq_len)
        dynamic: (batch_size, input_size, seq_len)
        chosen_idx: (batch_size,)
        """
        # last_task: (batch_size, 1)
        last_task = dynamic[:, 4, 0].clone().long().unsqueeze(1)
        # last_start_time: (batch_size, 1)
        last_start_time = dynamic[:, 5, 0].clone().unsqueeze(1)
        # last_position: (batch_size, 1)
        last_position = torch.gather(static[:, 1, :].clone(), 1, last_task)  # gather 收集输入的特定维度指定位置的数值 输出形状和idex一样
        # last_need_time: (batch_size, 1)
        last_need_time = torch.gather(static[:, 3, :].clone(), 1, last_task)

        # chosen_idx: (batch_size, 1)
        chosen_idx = chosen_idx.unsqueeze(1)
        # current_start_time_window: (batch_size, 1)
        current_start_time_window = torch.gather(static[:, 0, :].clone(), 1, chosen_idx)
        # current_position: (batch_size, 1)
        current_position = torch.gather(static[:, 1, :].clone(), 1, chosen_idx)
        # current_need_time: (batch_size, 1)
        current_need_time = torch.gather(static[:, 3, :].clone(), 1, chosen_idx)

        # last_end_time: (batch_size, 1)
        last_end_time = torch.add((MOVING_TIME_RATE * torch.abs(current_position - last_position)),
                                  torch.add(last_start_time, last_need_time))

        # two kind of situation
        # 1` current_start_time > last_end_time  current_start_time = current_start_time
        # 2` current_start_time < last_end_time  current_start_time = last_end_time
        # judge_result: (batch_size, 1)
        judge_result = current_start_time_window.ge(
            last_end_time).float()  # torch.ge(input) 对比每一个input和other是否有如下关系input ≥ other
        current_start_time = torch.add(torch.mul((1 - judge_result), last_end_time),
                                       torch.mul(judge_result, current_start_time_window))  # mul 元素对应相乘 mm 矩阵相乘

        # last_task: (batch_size, 1, seq_len)
        last_task = chosen_idx.unsqueeze(2).expand(-1, -1, dynamic.size(2)).float()  # expand 扩充tensor-1 表示当前维度不变；
        # last_start_time: (batch_size, 1, seq_len)
        last_start_time = current_start_time.unsqueeze(2).expand(-1, -1, dynamic.size(2)).float()

        # next_position: (batch_size, seq_len)
        next_position = static[:, 1, :].clone()
        # next_need_time: (batch_size, seq_len)
        next_need_time = static[:, 3, :].clone()
        # next_end_time: (batch_size, seq_len)
        next_end_time = static[:, 2, :].clone()

        # satisfy_time: (batch_size, seq_len)
        # next_end_time >   current_start_time + current_need_time +
        #                   0.2 * |next_position - current_position| + next_need_time
        satisfy_time = torch.add(torch.add((MOVING_TIME_RATE * torch.abs(next_position - current_position)),
                                           torch.add(current_start_time, current_need_time)), next_need_time)

        # satisfy_time_window: (batch_size, seq_len)
        satisfy_time_window = next_end_time.ge(satisfy_time)
        # have_time_window: (batch_size, 1)
        have_time_window = dynamic[:, 0, :].clone().sum(1).gt(0).unsqueeze(1)  # sum(dim),按维度求和 gt 严格大于 返回1
        # time_window: (batch_size, 1, seq_len)
        time_window = torch.mul(satisfy_time_window, have_time_window).unsqueeze(1).float()  # mul 维数可以自动扩充

        # access: (batch_size, seq_len)
        access = dynamic[:, 1, :].clone()
        # chosen_idx: (batch_size,)
        access.scatter_(1, chosen_idx, 0)
        # access: (batch_size, 1, seq_len)
        access = access.unsqueeze(1)

        # next_memory_consume: (batch_size, seq_len)
        next_memory_consume = static[:, 5, :].clone()
        # current_memory_surplus: (batch_size, seq_len)
        current_memory_surplus = dynamic[:, 2, :].clone()
        # satisfy_memory: (batch_size, seq_len)
        satisfy_memory = torch.sub(current_memory_surplus, next_memory_consume)
        satisfy_memory = torch.clamp(satisfy_memory, -1, self.memory_total)
        # satisfy_memory_now: (batch_size, 1, seq_len)
        satisfy_memory_now = satisfy_memory.ge(0).unsqueeze(1).float()
        # memory_consume: (batch_size, 1)
        memory_consume = torch.gather(static[:, 5, :].clone(), 1, chosen_idx)
        # memory_surplus: (batch_size, seq_len)
        memory_surplus = torch.sub(dynamic[:, 2, :].clone(), memory_consume)
        # memory_surplus: (batch_size, 1, seq_len)
        memory_surplus = torch.clamp(memory_surplus, 0.0, self.memory_total).unsqueeze(1)  # clamp:控制在范围内，0.0为下限

        # next_memory_surplus: (batch_size, seq_len)
        next_memory_surplus = memory_surplus.squeeze(1)
        # n_next_memory_consume: (batch_size, seq_len)
        n_next_memory_consume = static[:, 5, :].clone()
        next_satisfy_memory = torch.sub(next_memory_surplus, n_next_memory_consume)
        next_satisfy_memory = torch.clamp(next_satisfy_memory, -1, self.memory_total)
        # satisfy_memory_next: (batch_size, 1, seq_len)
        satisfy_memory_next = next_satisfy_memory.ge(0).unsqueeze(1).float()
        # satisfy_memory: (batch_size, 1, seq_len)
        satisfy_memory = torch.add(satisfy_memory_now, satisfy_memory_next)
        time_window = torch.mul(time_window, satisfy_memory)

        # task_power_consume: (batch_size, 1)
        task_power_consume = torch.gather(static[:, 6, :].clone(), 1, chosen_idx)
        # move_power_consume: (batch_size, 1)
        move_power_consume = MOVING_CONSUME_POWER_RATE * torch.abs(current_position - last_position)  # 机动过程中所消耗的电量

        # power_consume: (batch_size, 1)
        power_consume = torch.add(task_power_consume, move_power_consume)
        # power_surplus: (batch_size, seq_len)
        power_surplus = dynamic[:, 3, :].clone() - power_consume
        # power_surplus: (batch_size, 1, seq_len)
        power_surplus = torch.clamp(power_surplus, 0.0, self.power_total).unsqueeze(1)
        # have_power_surplus: (batch_size, 1)
        have_power_surplus = torch.neg(power_surplus[:, 0, :].sum(1).gt(0).float())
        # have_no_power_surplus: (batch_size, 1)
        have_no_power_surplus = torch.add(1.0, have_power_surplus).nonzero()
        # time_window: (batch_size, 1, seq_len)
        time_window[have_no_power_surplus, 0, :] = 0.0

        # new_dynamic: (batch_size, input_size, seq_len)
        new_dynamic = torch.cat((time_window, access, memory_surplus, power_surplus, last_task, last_start_time), dim=1)
        return new_dynamic

    def update_mask(self, dynamic):
        """
        Marks the visited city, so it can't be selected a second time
        mask: (batch_size, seq_len)
        dynamic: (batch_size, input_size, seq_len)
        chosen_idx: (batch_size,)
        """
        # dynamic: (batch_size, input_size, seq_len)
        # time_window: (batch_size, seq_len)
        time_window = dynamic[:, 0, :].clone()
        # time_mask: (batch_size, seq_len)
        time_mask = time_window.ne(0)

        # access: (batch_size, seq_len)
        access = dynamic[:, 1, :].clone()
        # access_mask: (batch_size, seq_len)
        access_mask = access.ne(0)

        # new_mask: (batch_size, seq_len)
        new_mask = torch.mul(time_mask, access_mask)

        # have_time_window: (batch_size, 1)
        have_time_window = new_mask.sum(1).gt(0).float()
        # have_no_time_window_index: (batch_size,)
        have_no_time_window_index = torch.sub(1, have_time_window.squeeze()).nonzero()

        new_mask[have_no_time_window_index, 0] = 1.
        # # # new_mask: (batch_size, seq_len)
        new_mask[have_no_time_window_index, 1:] = 0.

        if have_no_time_window_index.size(0) == time_window.size(0):
            new_mask[:, :] = 0.

        return new_mask.float()


def reward(static, tour_indices):
    """
    static: (batch_size, input_size, seq_len)
    tour_indices: (batch_size, tour_len)
    """
    # idx: (batch_size, static_size, tour_len)
    idx = tour_indices.unsqueeze(1).expand(-1, static.size(1), -1)
    # tour is coordinate data, static.data indexed by idx
    # tour: (batch_size, tour_len, input_size)
    tour = torch.gather(static.data, 2, idx).permute(0, 2, 1)
    # position_start: (batch_size, tour_len - 1)
    position_start = tour[:, :-1, 1]
    # position_start: (batch_size, tour_len - 1)
    position_end = tour[:, 1:, 1]
    # distance: (batch_size,)
    distance = torch.sum(torch.abs(position_end - position_start), dim=1)

    # power: (batch_size,)
    power = torch.sum(tour[:, :, 6], dim=1)

    # memory: (batch_size,)
    memory = torch.sum(tour[:, :, 5], dim=1)

    # revenue: (batch_size,)
    revenue = torch.sum(tour[:, :, 4], dim=1)
    # total_revenue: (batch_size,)
    total_revenue = torch.sum(static[:, 4, :], dim=1)
    # revenue_rate: (batch_size,)
    revenue_rate = torch.div(revenue, total_revenue)

    # reward: (batch_size,)
    reward = torch.add(torch.add(
        torch.mul(torch.div(revenue_rate, 1), REWARD_PROPORTION),
        torch.neg(torch.mul(torch.div(distance, POSITION_WIDTH * static.size(2) * 0.5), DISTANCE_PROPORTION))),
        torch.neg(torch.mul(torch.div(power, POWER_CONSUME * static.size(2) * 0.5), POWER_PROPORTION)))
    return torch.neg(reward), revenue_rate, distance, memory


def render(static, tour_indices, save_path, num=0):
    """
    Plots the found tours
    static: (batch_size, static_size, seq_len)
    tour_indices: (batch_size, seq_len)
    """
    static = static.cpu()
    tour_indices = tour_indices.cpu()
    plt.close('all')
    num_plots = 1
    _, ax11 = plt.subplots(nrows=num_plots, ncols=num_plots, sharex='col', sharey='row')
    # ang0 baseline
    ax11.plot([-0.1, START_TIME_WIDTH + 0.3], [0, 0], lw=2., c='grey', zorder=1, linestyle=':')

    # draw time window
    if HAVE_STATION == 1:
        flag1 = 0
        flag2 = 0
        for k in range(static.size(2)):
            station = static[0, 7, k]
            if flag1 == 0 and station.item() < 1:
                ax11.plot([static[0, 0, k], static[0, 2, k]], [static[0, 1, k], static[0, 1, k]],
                          lw=5., c='g', zorder=1, label='Time window')
                flag1 += 1
                ax11.legend()
            if flag1 == 1 and station.item() < 1:
                ax11.plot([static[0, 0, k], static[0, 2, k]], [static[0, 1, k], static[0, 1, k]],
                          lw=5., c='g', zorder=1)
            elif flag2 == 0 and station.item() == 1.:
                ax11.plot([static[0, 0, k], static[0, 2, k]], [static[0, 1, k], static[0, 1, k]],
                          lw=5., c='k', zorder=2, label='station')
                flag2 += 1
                ax11.legend()
            elif flag2 == 1 and station.item() == 1.:
                ax11.plot([static[0, 0, k], static[0, 2, k]], [static[0, 1, k], static[0, 1, k]],
                          lw=5., c='k', zorder=1)
    if HAVE_STATION == 0:
        for k in range(static.size(2)):
            if k == 0:
                ax11.plot([static[0, 0, k], static[0, 2, k]], [static[0, 1, k], static[0, 1, k]],
                          lw=5., c='g', zorder=1, label='Time window')
            else:
                ax11.plot([static[0, 0, k], static[0, 2, k]], [static[0, 1, k], static[0, 1, k]],
                          lw=5., c='g', zorder=1)
            ax11.legend()

    # convert the indices back into a tour
    # idx: (tour_len,)
    idx = tour_indices[0]
    if len(idx.size()) == 1:
        idx = idx.unsqueeze(0)
    # idx: (static_size, tour_len)
    idx = idx.expand(static.size(1), -1)
    # data: (static_size, tour_len)
    data = torch.gather(static[0].data, 1, idx).cpu().numpy()
    # choice color
    num = 0
    if num == 0:
        color = 'b'
    elif num == 1:
        color = 'mediumseagreen'
    elif num == 2:
        color = 'darkred'
    elif num == 3:
        color = 'palevioletred'
    else:
        color = 'darkslategray'

    # calculate every task's start_time
    start_time_window, position, end_time, require_time = data[0, :], data[1, :], data[2, :], data[3, :]
    seq_length = data.shape[1]
    start_time = [0.]
    for k in range(1, seq_length):
        last_end_time = start_time[k - 1] + require_time[k - 1] + MOVING_TIME_RATE * abs(
            position[k] - position[k - 1])
        if last_end_time <= start_time_window[k]:
            start_time.append(start_time_window[k])
        else:
            start_time.append(last_end_time)
    start_time = np.array(start_time)
    print("start_time", start_time)
    # scatter all task transfer point
    ax11.scatter(start_time, position, s=15, c='r', marker='o', zorder=3)
    ax11.scatter((start_time + require_time), position, s=15, c='r', marker='o', zorder=3)

    # draw line and legend, annotate order
    for k in range(seq_length - 1):
        if k == 0:
            ax11.plot([(start_time[k] + require_time[k]), start_time[k + 1]], [position[k], position[k + 1]],
                      alpha=0.5, c=color, lw=2., zorder=1, label='Task transfer')
            ax11.legend()
            plt.annotate('Start', xy=(start_time[k], position[k]), xytext=(0, -4),
                         fontsize=8, textcoords='offset points', ha='center', va='top')
        else:
            ax11.plot([(start_time[k] + require_time[k]), start_time[k + 1]], [position[k], position[k + 1]],
                      alpha=0.5, c=color, lw=2., zorder=1)
            plt.annotate('%s' % k, xy=(start_time[k], position[k]), xytext=(0, -4),
                         fontsize=8, textcoords='offset points', ha='center', va='top')

    # annotate the task order
    plt.annotate('End', xy=(start_time[seq_length - 1], position[seq_length - 1]),
                 xytext=(0, -10), fontsize=8, textcoords='offset points', ha='center', va='top')
    ax11.set_xlim(-0.3, START_TIME_WIDTH + 0.3)  # x value range limit
    ax11.set_ylim(-((POSITION_WIDTH / 2) + 0.05), (POSITION_WIDTH / 2) + 0.05)  # y value range limit

    plt.xlabel("Time")  # set x_label
    plt.ylabel("Side swing angle")  # set y_label
    plt.tight_layout()  # adapt subplot
    plt.savefig(save_path, bbox_inches='tight', dpi=600)  # save picture


if __name__ == '__main__':
    pass
