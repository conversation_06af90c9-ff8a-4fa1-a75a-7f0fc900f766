"""
Defines the main picture for training SMP
"""
import os
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.font_manager import FontProperties

# 配置中文字体支持
try:
    # 尝试设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
except:
    print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")

# 检查是否有可用的中文字体
def get_available_chinese_font():
    # 常见中文字体列表
    chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'FangSong', 'KaiTi', 'Arial Unicode MS', 'WenQuanYi Micro Hei']
    available_font = None
    
    for font_name in chinese_fonts:
        try:
            font = FontProperties(fname=mpl.font_manager.findfont(font_name))
            if font is not None:
                available_font = font
                print(f"使用中文字体: {font_name}")
                break
        except:
            continue
    
    return available_font

# 获取可用的中文字体
chinese_font = get_available_chinese_font()

def smooth_curve(points, factor=0.8):
    smoothed_points = []
    for point in points:
        if smoothed_points:
            previous = smoothed_points[-1]
            # 上一个节点*0.8+当前节点*0.2
            smoothed_points.append(previous * factor + point * (1 - factor))
        else:
            # 添加point
            smoothed_points.append(point)
    return smoothed_points


def plot_single_smp_train_loss(save_dir, times, losses, rewards, critic_rewards, revenue_rates, distances, memories, powers):
    plt.close('all')
    fig = plt.figure(figsize=(12, 21))  # 增加图像高度
    
    # 绘制actor loss
    ax1 = plt.subplot(7, 1, 1)  # 更改布局为7行1列
    ax1.plot(smooth_curve(losses), c='g')
    ax1.set_title('Actor Loss')
    ax1.set_xlabel('Step')
    ax1.set_ylabel('Actor Loss')

    # 绘制critic reward
    ax2 = plt.subplot(7, 1, 2)
    ax2.plot(smooth_curve(critic_rewards), c='b')
    ax2.set_xlabel('Step')
    ax2.set_ylabel('Reward')
    ax2.set_title('Critic Reward')

    # 绘制reward
    ax3 = plt.subplot(7, 1, 3)
    ax3.plot(smooth_curve(rewards), c='r')
    ax3.set_xlabel('Step')
    ax3.set_ylabel('Reward')
    ax3.set_title('Reward')
    
    # 绘制revenue rate
    ax4 = plt.subplot(7, 1, 4)
    ax4.plot(smooth_curve(revenue_rates), c='m')
    ax4.set_xlabel('Step')
    ax4.set_ylabel('Revenue Rate')
    ax4.set_title('Revenue Rate')
    
    # 绘制distance
    ax5 = plt.subplot(7, 1, 5)
    ax5.plot(smooth_curve(distances), c='c')
    ax5.set_xlabel('Step')
    ax5.set_ylabel('Distance')
    ax5.set_title('Distance')

    # 绘制power
    ax6 = plt.subplot(7, 1, 6)
    ax6.plot(smooth_curve(powers), c='y')
    ax6.set_xlabel('Step')
    ax6.set_ylabel('Power')
    ax6.set_title('Power Consumption')
    
    # 新增：绘制memory
    ax7 = plt.subplot(7, 1, 7)
    ax7.plot(smooth_curve(memories), c='k')  # 使用黑色
    ax7.set_xlabel('Step')
    ax7.set_ylabel('Memory')
    ax7.set_title('Memory Usage')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'train_loss_reward.png'), bbox_inches='tight', dpi=600)  # save picture
