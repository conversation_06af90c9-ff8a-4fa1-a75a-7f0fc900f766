import torch
import torch.nn as nn
import torch.nn.functional as F
from gpn import Encoder
from attention.attention import MultiHead_Additive_Attention
from indrnn.indrnn import IndRNN, IndRNNv2, IndRNN_Net

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
device_num = 0
if device == 'cuda':
    torch.cuda.set_device(device_num)
    torch.backends.cudnn.enabled = False

class Pointer(nn.Module):
    def __init__(self, hidden_size, num_layers=1, dropout=0.2,
                 attention='MultiHead_Additive_Attention', n_head=8,
                 rnn='gru', num_nodes=50):
        super(Pointer, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        if rnn == 'gru':
            self.gru = nn.GRU(hidden_size, hidden_size, num_layers,
                              batch_first=True,
                              dropout=dropout if num_layers > 1 else 0)
        elif rnn == 'lstm':
            self.gru = nn.LSTM(hidden_size, hidden_size, num_layers,
                               batch_first=True,
                               dropout=dropout if num_layers > 1 else 0)
        elif rnn == 'indrnn':
            self.gru = IndRNN_Net(hidden_size, hidden_size,
                                  num_nodes, num_layers, IndRNN)
        elif rnn == 'indrnnv2':
            self.gru = IndRNN_Net(hidden_size, hidden_size,
                                  num_nodes, num_layers, IndRNNv2)

        if attention == 'MultiHead_Additive_Attention':
            self.encoder_attn = MultiHead_Additive_Attention(hidden_size, n_head, device)

        self.drop_rnn = nn.Dropout(p=dropout)
        self.drop_hh = nn.Dropout(p=dropout)

    def forward(self, static_hidden, dynamic_hidden, decoder_hidden, last_hh):
        # static_hidden: (batch_size, hidden_size, seq_len)
        # dynamic_hidden: (batch_size, hidden_size, seq_len)
        # decoder_hidden: (batch_size, hidden_size, 1)
        # rnn_out: (batch_size, 1, hidden_size)
        # last_hh: (1, batch_size, hidden_size)
        rnn_out, last_hh = self.gru(decoder_hidden.transpose(2, 1), last_hh)
        # rnn_out: (batch_size, hidden_size)
        rnn_out = self.drop_rnn(rnn_out.squeeze(1))
        if self.num_layers == 1:
            # last_hh: (1, batch_size, hidden_size)
            # last_hh = self.drop_hh(last_hh)
            pass
        # probs: (batch_size, seq_len)
        probs = self.encoder_attn(static_hidden, dynamic_hidden, rnn_out)
        return probs, last_hh


class PN4SMP(nn.Module):
    def __init__(self, static_size, dynamic_size, hidden_size, task,
                 update_fn=None, mask_fn=None, num_layers=1, dropout=0.,
                 attention='Additive_Attention_Glimpse', n_head=8,
                 rnn='indrnnv2', num_nodes=50, encoder='conv1d'):
        super(PN4SMP, self).__init__()
        if dynamic_size < 1:
            raise ValueError(':param dynamic_size: must be > 0, even if the '
                             'problem has no dynamic elements')
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.task = task
        if encoder == 'conv1d':
            self.static_encoder = Encoder(static_size, hidden_size)
            self.dynamic_encoder = Encoder(dynamic_size, hidden_size)
        self.decoder = Encoder(static_size, hidden_size)
        self.pointer = Pointer(hidden_size, num_layers, dropout,
                               attention, n_head,
                               rnn, num_nodes)
        for p in self.parameters():
            if len(p.shape) > 1:
                nn.init.xavier_uniform_(p)
        self.x0 = torch.zeros((1, static_size, 1), requires_grad=True, device=device)

        print(task, num_nodes)
        print(encoder)
        print(rnn, num_layers)
        print(attention, n_head)

    def forward(self, static, dynamic, decoder_input=None, last_hh=None):
        # static: (batch_size, static_size, seq_len)
        # dynamic: (batch_size, dynamic_size, seq_len)
        # decoder_input: None
        batch_size, input_size, seq_len = static.size()
        if decoder_input is None:
            # decoder_input: (batch_size, input_size, 1)
            decoder_input = self.x0.expand(batch_size, -1, -1)
        # mask: (batch_size, seq_len)
        mask = torch.ones(batch_size, seq_len, device=device)
        if self.task == 'single_smp':
            mask[:, 1:] = 0.
        tour_idx, tour_logp = [], []
        max_steps = seq_len if self.mask_fn is None else 10000

        # static_hidden: (batch_size, hidden_size, seq_len)
        static_hidden = self.static_encoder(static)
        # dynamic_hidden: (batch_size, hidden_size, seq_len)
        dynamic_hidden = self.dynamic_encoder(dynamic)
        # decoder_hidden: (batch_size, hidden_size, 1)
        decoder_hidden = self.decoder(decoder_input)

        for _ in range(max_steps):
            # Satisfying Termination Conditions and break
            if not mask.byte().any():
                break

            # Using PointerNetwork to obtain the probability of each node
            # probs: (batch_size, seq_len)
            # last_hh: (1, batch_size, hidden_size)
            probs, last_hh = self.pointer(static_hidden,
                                          dynamic_hidden,
                                          decoder_hidden, last_hh)
            probs = F.softmax(probs + mask.log(), dim=1)
            # Searching nodes according to probs
            if self.training:
                m = torch.distributions.Categorical(probs)
                # ptr: (batch_size,)
                ptr = m.sample()
                while not torch.gather(mask, 1, ptr.data.unsqueeze(1)).byte().all():
                    # sample a node that does not masked
                    ptr = m.sample()
                # logp: (batch_size,)
                logp = m.log_prob(ptr)
            else:
                # Greedy during testing
                # prob: (batch_size,)
                # ptr: (batch_size,)
                prob, ptr = torch.max(probs, 1)
                # logp: (batch_size,)
                logp = prob.log()

            # Updating dynamic, dynamic_hidden and update mask
            if self.task == 'single_smp':
                # dynamic: (batch_size, input_size, seq_len)
                dynamic = self.update_fn(static, dynamic, ptr.data)
                # dynamic_hidden: (batch_size, hidden_size, seq_len)
                dynamic_hidden = self.dynamic_encoder(dynamic)
                # is_done: (batch_size,)
                is_done = dynamic[:, 1].sum(1).eq(0).float()
                # logp: (batch_size,)
                logp = logp * (1. - is_done)
            # mask: (batch_size, seq_len)
            mask = self.mask_fn(dynamic).detach()

            # Saving the probability of selecting each node and the pointing node
            # tour_logp: (seq_len * tensor(batch_size, 1))
            tour_logp.append(logp.unsqueeze(1))
            # tour_idx: [seq_len * tensor(batch_size, 1)]
            tour_idx.append(ptr.data.unsqueeze(1))

            # Updating decoder_input and update decoder_hidden
            # decoder_input: (batch_size, input_size, 1)
            # static: (batch_size, input_size, seq_len)
            decoder_input = torch.gather(static, 2,
                                         ptr.view(-1, 1, 1)
                                         .expand(-1, input_size, 1)).detach()
            # decoder_hidden: (batch_size, hidden_size, 1)
            decoder_hidden = self.decoder(decoder_input)

        # Converting tour_idx and tour_logp to tensor and return
        # tour_idx: (batch_size, seq_len)
        tour_idx = torch.cat(tour_idx, dim=1)
        # tour_logp: (batch_size, seq_len)
        tour_logp = torch.cat(tour_logp, dim=1)
        return tour_idx, tour_logp
