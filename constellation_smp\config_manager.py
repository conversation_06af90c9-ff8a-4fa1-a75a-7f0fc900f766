"""
配置管理器
统一管理训练和模型配置
"""
import json
import os
from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any


@dataclass
class ModelConfig:
    """模型配置"""
    model_type: str = 'gpn_transformer'
    static_size: int = 9
    dynamic_size: int = 7
    hidden_size: int = 256
    d_model: int = 256
    n_head: int = 8
    n_transformer_layers: int = 6
    d_ff: int = 1024
    transformer_dropout: float = 0.1
    dropout: float = 0.1
    rnn: str = 'indrnn'
    num_layers: int = 2
    num_satellites: int = 3
    constellation_mode: str = 'competitive'


@dataclass
class TrainingConfig:
    """训练配置"""
    task: str = 'constellation_smp'
    num_nodes: int = 100
    batch_size: int = 32
    epochs: int = 3  # 增加训练轮数
    train_size: int = 100000  # 增加训练数据量
    valid_size: int = 10000  # 增加验证数据量
    actor_lr: float = 1e-4  # 降低学习率
    critic_lr: float = 1e-4  # 降低学习率
    weight_decay: float = 1e-4  # 增加权重衰减
    max_grad_norm: float = 1.0  # 降低梯度裁剪阈值
    seed: int = 12346
    verbose: bool = True


@dataclass
class DataConfig:
    """数据配置"""
    memory_total: float = 0.3
    power_total: float = 5.0
    task_sharing: bool = True
    communication_delay: float = 0.01
    satellite_distance: float = 0.5


@dataclass
class SystemConfig:
    """系统配置"""
    checkpoint: Optional[str] = None
    test: bool = False
    attention: str = 'MultiHead_Additive_Attention'
    encoder: str = 'conv1d'


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.model_config = ModelConfig()
        self.training_config = TrainingConfig()
        self.data_config = DataConfig()
        self.system_config = SystemConfig()
        
        if config_path and os.path.exists(config_path):
            self.load_from_file(config_path)
    
    def update_from_args(self, args):
        """从argparse参数更新配置"""
        # 更新模型配置
        for field in self.model_config.__dataclass_fields__:
            if hasattr(args, field):
                setattr(self.model_config, field, getattr(args, field))
        
        # 更新训练配置
        for field in self.training_config.__dataclass_fields__:
            if hasattr(args, field):
                setattr(self.training_config, field, getattr(args, field))
        
        # 更新数据配置
        for field in self.data_config.__dataclass_fields__:
            if hasattr(args, field):
                setattr(self.data_config, field, getattr(args, field))
        
        # 更新系统配置
        for field in self.system_config.__dataclass_fields__:
            if hasattr(args, field):
                setattr(self.system_config, field, getattr(args, field))
    
    def save_to_file(self, config_path: str):
        """保存配置到文件"""
        config_dict = {
            'model': asdict(self.model_config),
            'training': asdict(self.training_config),
            'data': asdict(self.data_config),
            'system': asdict(self.system_config)
        }
        
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def load_from_file(self, config_path: str):
        """从文件加载配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        if 'model' in config_dict:
            for key, value in config_dict['model'].items():
                if hasattr(self.model_config, key):
                    setattr(self.model_config, key, value)
        
        if 'training' in config_dict:
            for key, value in config_dict['training'].items():
                if hasattr(self.training_config, key):
                    setattr(self.training_config, key, value)
        
        if 'data' in config_dict:
            for key, value in config_dict['data'].items():
                if hasattr(self.data_config, key):
                    setattr(self.data_config, key, value)
        
        if 'system' in config_dict:
            for key, value in config_dict['system'].items():
                if hasattr(self.system_config, key):
                    setattr(self.system_config, key, value)
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            'model': asdict(self.model_config),
            'training': asdict(self.training_config),
            'data': asdict(self.data_config),
            'system': asdict(self.system_config)
        }
    
    def validate_config(self) -> tuple[bool, list[str]]:
        """验证配置的有效性"""
        errors = []
        
        # 验证模型配置
        if self.model_config.model_type not in ['gpn', 'gpn_transformer']:
            errors.append(f"不支持的模型类型: {self.model_config.model_type}")
        
        if self.model_config.num_satellites <= 0:
            errors.append("卫星数量必须大于0")
        
        if self.model_config.constellation_mode not in ['cooperative', 'competitive', 'hybrid']:
            errors.append(f"不支持的星座模式: {self.model_config.constellation_mode}")
        
        # 验证训练配置
        if self.training_config.batch_size <= 0:
            errors.append("批次大小必须大于0")
        
        if self.training_config.epochs <= 0:
            errors.append("训练轮数必须大于0")
        
        if self.training_config.actor_lr <= 0 or self.training_config.critic_lr <= 0:
            errors.append("学习率必须大于0")
        
        # 验证数据配置
        if self.data_config.memory_total <= 0 or self.data_config.power_total <= 0:
            errors.append("内存和能量总量必须大于0")
        
        return len(errors) == 0, errors
    
    def create_experiment_name(self) -> str:
        """创建实验名称"""
        import datetime
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        
        name_parts = [
            self.training_config.task,
            f"nodes{self.training_config.num_nodes}",
            f"sats{self.model_config.num_satellites}",
            self.model_config.model_type,
            self.model_config.constellation_mode,
            timestamp
        ]
        
        return "_".join(name_parts)
    
    def get_model_params(self) -> Dict[str, Any]:
        """获取模型参数"""
        return asdict(self.model_config)
    
    def get_training_params(self) -> Dict[str, Any]:
        """获取训练参数"""
        return asdict(self.training_config)
    
    def get_data_params(self) -> Dict[str, Any]:
        """获取数据参数"""
        return asdict(self.data_config)
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("=" * 50)
        print("配置摘要")
        print("=" * 50)
        
        print("\n模型配置:")
        for key, value in asdict(self.model_config).items():
            print(f"  {key}: {value}")
        
        print("\n训练配置:")
        for key, value in asdict(self.training_config).items():
            print(f"  {key}: {value}")
        
        print("\n数据配置:")
        for key, value in asdict(self.data_config).items():
            print(f"  {key}: {value}")
        
        print("\n系统配置:")
        for key, value in asdict(self.system_config).items():
            print(f"  {key}: {value}")
        
        print("=" * 50)


def create_default_config() -> ConfigManager:
    """创建默认配置"""
    return ConfigManager()


def load_config_from_file(config_path: str) -> ConfigManager:
    """从文件加载配置"""
    return ConfigManager(config_path)


def merge_configs(base_config: ConfigManager, override_config: Dict[str, Any]) -> ConfigManager:
    """合并配置"""
    merged_config = ConfigManager()
    
    # 复制基础配置
    merged_config.model_config = base_config.model_config
    merged_config.training_config = base_config.training_config
    merged_config.data_config = base_config.data_config
    merged_config.system_config = base_config.system_config
    
    # 应用覆盖配置
    for category, params in override_config.items():
        if category == 'model' and hasattr(merged_config, 'model_config'):
            for key, value in params.items():
                if hasattr(merged_config.model_config, key):
                    setattr(merged_config.model_config, key, value)
        elif category == 'training' and hasattr(merged_config, 'training_config'):
            for key, value in params.items():
                if hasattr(merged_config.training_config, key):
                    setattr(merged_config.training_config, key, value)
        elif category == 'data' and hasattr(merged_config, 'data_config'):
            for key, value in params.items():
                if hasattr(merged_config.data_config, key):
                    setattr(merged_config.data_config, key, value)
        elif category == 'system' and hasattr(merged_config, 'system_config'):
            for key, value in params.items():
                if hasattr(merged_config.system_config, key):
                    setattr(merged_config.system_config, key, value)
    
    return merged_config
