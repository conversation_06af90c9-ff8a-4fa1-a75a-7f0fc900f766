import numpy as np
import torch

num_samples = 10
size = 5
earth_stations = torch.zeros(num_samples, 1, size)
chosen_idx = torch.from_numpy(np.random.randint(0, size, (num_samples, 1)))
# torch.gather(earth_stations[:, 0, :],1,chosen_idx)
station_idx = torch.gather(earth_stations[:, 0, :],1,chosen_idx)
station_idx =1
print(station_idx)
# station_idx, _ = torch.max(earth_stations, dim=2)
# station_idx = station_idx.expand(num_samples, size).unsqueeze(1)
# station_idx = torch.sub(earth_stations, station_idx).eq(0).float()
# memory_consume = torch.from_numpy(np.random.uniform(0, 1, (num_samples, 1, size))).float()
# station_idx = 10 * station_idx.int()
# memory_consume = torch.sub(memory_consume, station_idx)
# print(memory_consume)

# memory_consume = memory_consume - station_idx

