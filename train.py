"""
Defines the main trainer model for SMP

Each task must define the following functions:
* mask_fn: can be None
* update_fn: can be None
* reward_fn: specifies the quality of found solutions
* render_fn: Specifies how to plot found solutions. Can be None
"""
import os
import time
import datetime
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from gpn import GPN4SMP, Encoder
from pn import PN4SMP
from pict import plot_single_smp_train_loss
from torch.optim import *
from hyperparameter import args
from single_smp import single_smp
from single_smp.single_smp import SMPDataset


class StateCritic(nn.Module):
    """
    Estimates the problem complexity
    This is a basic module that just looks at the log-probabilities predicted by
    the encoder + decoder, and returns an estimate of complexity
    """

    def __init__(self, static_size, dynamic_size, hidden_size):
        super(StateCritic, self).__init__()
        self.element_size = static_size + dynamic_size
        self.element_encoder = Encoder(self.element_size, hidden_size)

        # Define the encoder & decoder models
        self.fc1 = nn.Conv1d(hidden_size, 20, kernel_size=1)
        self.fc2 = nn.Conv1d(20, 20, kernel_size=1)
        self.fc3 = nn.Conv1d(20, 1, kernel_size=1)
        # initialize parameters with xavier
        for p in self.parameters():
            if len(p.shape) > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, static, dynamic):
        # Use the probabilities of visiting each
        # element: (batch_size, element_size, seq_len)
        # element_hidden: (batch_size, hidden_size, seq_len)
        element = torch.cat((static, dynamic), dim=1)
        element_hidden = self.element_encoder(element)

        output = F.relu(self.fc1(element_hidden))
        output = F.relu(self.fc2(output))
        # output: (batch_size, 1, 1)
        output = self.fc3(output).sum(dim=2)
        return output


def validate_single_smp(data_loader, actor, reward_fn, render_fn=None, save_dir='.', num_plot=5):
    # Used to monitor progress on a validation set & optionally plot solution
    actor.eval()
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    rewards = []
    for batch_idx, batch in enumerate(data_loader):
        static, dynamic, x0 = batch
        static = static.to(device)
        dynamic = dynamic.to(device)
        with torch.no_grad():
            tour_indices, _ = actor.forward(static, dynamic)
        reward = reward_fn(static, tour_indices)[0].mean().item()
        rewards.append(reward)
        if render_fn is not None and batch_idx < num_plot:
            name = 'test_batch%d_%2.4f.png' % (batch_idx, -reward)
            path = os.path.join(save_dir, name)
            render_fn(static, tour_indices, path)
    actor.train()
    return np.mean(rewards)


def train_single_smp():
    train_data = SMPDataset(args.num_nodes, args.train_size, args.seed, args.memory_total, args.power_total)
    valid_data = SMPDataset(args.num_nodes, args.valid_size, args.seed + 1, args.memory_total, args.power_total)
    if args.model =='gpn':
        actor = GPN4SMP(args.static_size,
                        args.dynamic_size,
                        args.hidden_size,
                        args.task,
                        args.rnn,
                        args.num_layers,
                        train_data.update_dynamic,
                        train_data.update_mask,
                        args.num_nodes,
                        ).to(device)
    elif args.model == 'pn':
        actor = PN4SMP(args.static_size,
                       args.dynamic_size,
                       args.hidden_size,
                       args.task,
                       train_data.update_dynamic,
                       train_data.update_mask,
                       args.num_layers,
                       args.dropout,
                       args.attention,
                       args.n_head,
                       args.rnn,
                       args.num_nodes,
                       ).to(device)

    critic = StateCritic(args.static_size, args.dynamic_size, args.hidden_size).to(device)

    # transfer args to dictionary
    kwargs = vars(args)
    kwargs['train_data'] = train_data
    kwargs['valid_data'] = valid_data
    kwargs['reward_fn'] = single_smp.reward
    kwargs['render_fn'] = single_smp.render

    if args.checkpoint:
        path = os.path.join(args.checkpoint, 'actor.pt')
        actor.load_state_dict(torch.load(path, device))
        path = os.path.join(args.checkpoint, 'critic.pt')
        critic.load_state_dict(torch.load(path, device))

    if not args.test:
        train_single_smp_process(actor, critic, **kwargs)

    test_data = SMPDataset(args.num_nodes, args.valid_size, args.seed + 2, args.memory_total, args.power_total)
    test_dir = 'test'
    test_loader = DataLoader(test_data, args.batch_size, False, num_workers=0)
    out = validate_single_smp(test_loader, actor, single_smp.reward, single_smp.render, test_dir, num_plot=5)
    print('Average tour revenue rate: ', -out)


def train_single_smp_process(actor, critic, task, num_nodes, train_data, valid_data, reward_fn,
                             render_fn, batch_size, actor_lr, critic_lr, max_grad_norm, attention, epochs,
                             **kwargs):
    # create a file directory save_dir
    now = '%s' % datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    print(now)
    save_dir = os.path.join(task, task + '%d' % num_nodes, str(args.model)+str(args.rnn)+now)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # save information in log.txt
    f_dir = os.path.join(save_dir, 'log.txt')
    f = open(f_dir, 'a+')
    f.write(task + ': ' + str(num_nodes) + '\n'
            + 'model: ' + str(args.model) + '\n'
            + 'rnn: ' + str(args.rnn) + '\n'
            + 'hidden_size: ' + str(args.hidden_size) + '\n'
            + 'batch_size: ' + str(args.batch_size) + '\n'
            + 'seed: ' + str(args.seed) + '\n'
            + 'train-size: ' + str(args.train_size) + '\n'
            + 'valid-size: ' + str(args.valid_size) + '\n'
            + 'epochs: ' + str(args.epochs) + '\n'
            + 'lr: ' + str(args.lr) + '\n'
            + 'memory_total: ' + str(args.memory_total) + '\n'
            + 'power_total: ' + str(args.power_total) + '\n'
            + 'dropout: ' + str(args.dropout) + '\n'
            + 'actor_lr: ' + str(args.actor_lr) + '\n'
            + 'critic_lr: ' + str(args.critic_lr) + '\n'
            + now + '\n')

    f.close()

    # define the optimizer
    actor_optim = optim.Adam(actor.parameters(), lr=actor_lr)
    critic_optim = optim.Adam(critic.parameters(), lr=critic_lr)
    
    # 使用余弦退火学习率调度器，提高训练效果
    total_steps = len(train_data) // batch_size * epochs
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        actor_optim, 
        T_0=total_steps // 10,  # 第一次重启的周期
        T_mult=2,  # 每次重启后周期长度的倍数
        eta_min=1e-6  # 最小学习率
    )
    critic_scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        critic_optim, 
        T_0=total_steps // 10,
        T_mult=2,
        eta_min=1e-6
    )

    # define the DataLoader
    train_loader = DataLoader(train_data, batch_size, True, num_workers=0)
    valid_loader = DataLoader(valid_data, batch_size, False, num_workers=0)
    best_params = None
    best_reward = np.inf
    times, losses, rewards, critic_rewards, revenue_rates, distances, memories = [], [], [], [], [], [], []

    # start training epoch
    for epoch in range(epochs):
        # create a file directory epoch_dir
        epoch_dir = os.path.join(save_dir, 'epoch%s' % epoch)
        if not os.path.exists(epoch_dir):
            os.makedirs(epoch_dir)

        # start training
        actor.train()
        critic.train()
        epoch_start = time.time()
        start = epoch_start
        for batch_idx, batch in enumerate(train_loader):
            # scheduler.step()
            static, dynamic, x0 = batch
            static = static.to(device)
            dynamic = dynamic.to(device)

            # Full forward pass through the dataset
            # tour_indices: (batch_size, seq_len)
            # tour_logp: (batch_size, seq_len)
            if args.model == 'gpn':
                tour_indices, tour_logp = actor(static, dynamic)
            elif args.model == 'pn':
                x0 = x0.to(device) if len(x0) > 0 else None
                tour_indices, tour_logp = actor(static, dynamic, x0)

            # Sum the log probabilities for each city in the tour
            # reward: (batch_size,)
            reward, revenue_rate, distance, memory = reward_fn(static, tour_indices)

            # Query the critic for an estimate of the reward
            # critic_est: (batch_size,)
            critic_est = critic(static, dynamic).view(-1)

            # advantage: (batch_size,)
            advantage = (reward - critic_est)

            # actor_loss: scalar
            actor_loss = torch.mean(advantage.detach() * tour_logp.sum(dim=1))

            # critic_loss: scalar
            critic_loss = torch.mean(advantage ** 2)

            # actor gradient update
            actor_optim.zero_grad()
            actor_loss.backward()
            torch.nn.utils.clip_grad_norm_(actor.parameters(), max_grad_norm)
            actor_optim.step()

            # critic gradient update
            critic_optim.zero_grad()
            critic_loss.backward()
            torch.nn.utils.clip_grad_norm_(critic.parameters(), max_grad_norm)
            critic_optim.step()

            scheduler.step()
            critic_scheduler.step()

            # save critic_rewards, rewards and losses to list
            critic_rewards.append(-torch.mean(critic_est.detach()).item())
            rewards.append(-torch.mean(reward.detach()).item())
            losses.append(torch.mean(actor_loss.detach()).item())
            revenue_rates.append(torch.mean(revenue_rate.detach()).item())
            distances.append(torch.mean(distance.detach()).item())
            memories.append(torch.mean(memory.detach()).item())

            # print every step message
            print('Batch %d, reward: %2.3f, revenue_rate: %2.3f, distance: %2.3f, memory: %2.3f, loss: %2.4f, lr:%.7f'
                  % (batch_idx, rewards[-1], revenue_rates[-1], distances[-1], memories[-1], losses[-1],
                     actor_optim.state_dict()['param_groups'][0]['lr']))

            if (batch_idx + 1) % 100 == 0:
                end = time.time()
                # save times
                times.append(end - start)
                start = end
                mean_loss = np.mean(losses[-100:])
                mean_reward = np.mean(rewards[-100:])
                mean_revenue_rate = np.mean(revenue_rates[-100:])
                mean_distance = np.mean(distances[-100:])
                mean_memory = np.mean(memories[-100:])

                # plot train actor_loss, rewards and critic_rewards
                plot_single_smp_train_loss(losses, rewards, critic_rewards, epoch_dir)

                # print message after 100 steps
                print(
                    'Batch %d/%d, reward: %2.3f, revenue_rate: %2.3f, '
                    'distance: %2.3f, power: %2.4f, loss: %2.4f, lr:%.7f, took: %2.4fs' %
                    (batch_idx, len(train_loader), float(mean_reward), float(mean_revenue_rate),
                     float(mean_distance), float(mean_memory), float(mean_loss),
                     actor_optim.state_dict()['param_groups'][0]['lr'], times[-1]))
                f = open(f_dir, 'a+')
                f.write(
                    'Batch %d/%d, reward: %2.3f, revenue_rate: %2.3f, '
                    'distance: %2.3f, power: %2.4f, loss: %2.4f, lr:%.7f, took: %2.4fs\n' %
                    (batch_idx, len(train_loader), float(mean_reward), float(mean_revenue_rate),
                     float(mean_distance), float(mean_memory), float(mean_loss),
                     actor_optim.state_dict()['param_groups'][0]['lr'], times[-1]))
                f.close()

        # validate and save rendering of validation set tours
        mean_valid = validate_single_smp(valid_loader, actor, reward_fn, render_fn, epoch_dir, num_plot=5)

        #  calculate mean value
        mean_loss = np.mean(losses)
        mean_reward = np.mean(rewards)

        # Save the actor and critic weights
        save_path = os.path.join(epoch_dir, 'actor.pt')
        torch.save(actor.state_dict(), save_path)
        save_path = os.path.join(epoch_dir, 'critic.pt')
        torch.save(critic.state_dict(), save_path)

        # save best model parameters
        if mean_valid < best_reward:
            best_reward = mean_valid
            save_path = os.path.join(save_dir, 'actor.pt')
            torch.save(actor.state_dict(), save_path)
            save_path = os.path.join(save_dir, 'critic.pt')
            torch.save(critic.state_dict(), save_path)

        print('Mean epoch loss/reward: %2.4f, %2.4f, %2.4f, took: %2.4fs '
              '(%2.4fs / 100 batches)\n' % (float(mean_loss), float(mean_reward), float(mean_valid),
                                            time.time() - epoch_start, float(np.mean(times))))
        f = open(f_dir, 'a+')
        f.write('Epoch %d mean epoch loss/reward: %2.4f, %2.4f, %2.4f, took: %2.4fs '
                '(%2.4fs / 100 batches)\n' % (epoch, float(mean_loss), float(mean_reward), float(mean_valid),
                                              time.time() - epoch_start, float(np.mean(times))))
        f.close()


if __name__ == '__main__':

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    device_num = 0
    print(device, device_num)
    if device == 'cuda':
        torch.cuda.set_device(device_num)
        # 启用cudnn自动优化，提升计算性能
        torch.backends.cudnn.benchmark = True

    if args.task == 'single_smp':
        train_single_smp()
    else:
        raise ValueError('Task <%s> not understood' % args.task)
