import matplotlib.pyplot as plt
import os

task = 'single_smp'
n = 100
steps = 125


log_files = [
            # '2021_06_04_22_23_12',
             '2021_06_05_12_06_48',
             # '2021_06_05_17_03_45'
             ]

plt.close('all')

def smooth_curve(points, factor=0.8):
    smoothed_points = []
    for point in points:
        if smoothed_points:
            previous = smoothed_points[-1]
            # 上一个节点*0.8+当前节点*0.2
            smoothed_points.append(previous * factor + point * (1 - factor))
        else:
            # 添加point
            smoothed_points.append(point)
    return smoothed_points

color_list = ['blue', 'green', 'red', 'purple', 'y', 'ivory', 'firebrick', 'deeppink', 'darkgray','coral']
rewards = {}
losses = {}
for log_file in log_files:
    file = os.path.join('..', task, task + str(n), log_file, 'log.txt')
    with open(file, 'r') as f:
        lines = f.readlines()
        if log_file == '2021_06_04_22_23_12':
            model = 'pn+indrnn'
        if log_file == '2021_06_05_12_06_48':
            model = 'GPN-IndRNN'
        if log_file == '2021_06_05_17_03_45':
            model = 'Gpn+lstm'
        reward = []
        loss = []
        count = 0

        for line in lines[15:]:
            if line[0] == 'B':
                reward.append(float(line.split('reward: ')[1][:5]))
                loss.append(float(line.split('loss: ')[1][:5]))
                count += 1
            if count == steps:
                rewards[model] = reward
                losses[model] = loss
                break

        f.close()

plt.subplot(2, 1, 1)
count = 0
for model, reward in rewards.items():
    plt.plot(smooth_curve(reward), color=color_list[9], label=model)
    count += 1
plt.legend()

plt.xlabel('step * 100')
plt.ylabel('Reward')

count = 0
plt.subplot(2, 1, 2)
for model, reward in losses.items():
    plt.plot(smooth_curve(reward), color=color_list[count], label=model)
    count += 1
plt.legend()

plt.xlabel('step * 100')
plt.ylabel('Loss')

# plt.show()
plt.tight_layout()  # adapt subplot
plt.savefig(os.path.join('..', task, task + str(n), task + str(n) + '.png'))
