🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_07_19_59_08
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 64
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_07_19_59_12
使用模型: gpn_transformer
Actor参数数量: 178,724
Critic参数数量: 87,201

开始训练 Epoch 1/3
Batch 0: Reward: -9.8308, Loss: 111.1892, Revenue: 0.3429, LoadBalance: 0.6632, Tasks: [S0:636(23.1%), S1:1018(37.0%), S2:1098(39.9%)], ActorGrad: 38.1170, CriticGrad: 615.5419, Advantage: μ=-1.200, σ=0.352, range=[-1.91, -0.64]

📊 Epoch 1 训练统计:
  平均奖励: -9.8642
  平均损失: 90.3220
  平均收益率: 0.3528
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -10.576, revenue_rate: 0.3373, efficiency: 0.1281, distance: 5.8283, memory: 0.0001, power: 0.1848
Test Summary - Avg reward: -10.363±2.806, revenue_rate: 0.3378±0.0383, efficiency: 0.1345, completion_rate: 0.3980, distance: 6.0942, memory: 0.0543, power: 0.1932
Load Balance - Avg balance score: 0.6418±0.1584
Task Distribution by Satellite:
  Satellite 1: 1083 tasks (27.83%)
  Satellite 2: 1101 tasks (28.29%)
  Satellite 3: 1708 tasks (43.88%)
✅ 验证完成 - Epoch 1, reward: -10.363, revenue_rate: 0.3378, distance: 6.0942, memory: 0.0543, power: 0.1932
  ✅ 训练验证差距正常: 0.4985
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_07_19_59_12 (验证集奖励: -10.3627)

开始训练 Epoch 2/3
Batch 0: Reward: -9.1316, Loss: 60.4067, Revenue: 0.3533, LoadBalance: 0.7240, Tasks: [S0:768(27.3%), S1:1085(38.5%), S2:963(34.2%)], ActorGrad: 51.1709, CriticGrad: 190.3506, Advantage: μ=-1.185, σ=0.400, range=[-2.41, -0.41]

📊 Epoch 2 训练统计:
  平均奖励: -8.1805
  平均损失: 44.7323
  平均收益率: 0.3363
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -9.408, revenue_rate: 0.3463, efficiency: 0.1379, distance: 5.7351, memory: 0.0260, power: 0.1945
Test Summary - Avg reward: -9.488±3.064, revenue_rate: 0.3301±0.0396, efficiency: 0.1255, completion_rate: 0.3795, distance: 5.4838, memory: 0.0104, power: 0.1846
Load Balance - Avg balance score: 0.6457±0.1676
Task Distribution by Satellite:
  Satellite 1: 1385 tasks (37.35%)
  Satellite 2: 856 tasks (23.09%)
  Satellite 3: 1467 tasks (39.56%)
✅ 验证完成 - Epoch 2, reward: -9.488, revenue_rate: 0.3301, distance: 5.4838, memory: 0.0104, power: 0.1846
  ✅ 训练验证差距正常: 1.3079
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_07_19_59_12 (验证集奖励: -9.4883)

开始训练 Epoch 3/3
Batch 0: Reward: -7.7637, Loss: 39.0738, Revenue: 0.3054, LoadBalance: 0.7455, Tasks: [S0:757(31.1%), S1:819(33.7%), S2:856(35.2%)], ActorGrad: 62.1416, CriticGrad: 72.6771, Advantage: μ=-1.120, σ=0.559, range=[-2.94, 0.01]

📊 Epoch 3 训练统计:
  平均奖励: -7.8353
  平均损失: 37.8996
  平均收益率: 0.3179
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -10.067, revenue_rate: 0.3443, efficiency: 0.1436, distance: 6.0575, memory: 0.0118, power: 0.2059
Test Summary - Avg reward: -10.192±2.811, revenue_rate: 0.3605±0.0396, efficiency: 0.1577, completion_rate: 0.4369, distance: 6.5578, memory: 0.0308, power: 0.2155
Load Balance - Avg balance score: 0.6604±0.1498
Task Distribution by Satellite:
  Satellite 1: 1616 tasks (37.65%)
  Satellite 2: 1016 tasks (23.67%)
  Satellite 3: 1660 tasks (38.68%)
✅ 验证完成 - Epoch 3, reward: -10.192, revenue_rate: 0.3605, distance: 6.5578, memory: 0.0308, power: 0.2155
  ⚠️ 过拟合: 训练验证差距 = 2.3568
训练完成

开始测试模型...
Test Batch 2/2, reward: -10.209, revenue_rate: 0.3441, efficiency: 0.1439, distance: 6.0647, memory: 0.0474, power: 0.2011
Test Summary - Avg reward: -9.958±3.083, revenue_rate: 0.3629±0.0423, efficiency: 0.1592, completion_rate: 0.4380, distance: 6.3141, memory: 0.0216, power: 0.2174
Load Balance - Avg balance score: 0.6563±0.1606
Task Distribution by Satellite:
  Satellite 1: 1560 tasks (36.35%)
  Satellite 2: 1006 tasks (23.44%)
  Satellite 3: 1726 tasks (40.21%)
测试完成 - 平均奖励: -9.958, 平均星座收益率: 0.3629
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_07_19_59_12
   平均奖励: -9.9584
   收益率: 0.3629

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 64
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_07_20_07_56
使用模型: gpn_transformer
Actor参数数量: 178,724
Critic参数数量: 87,201

开始训练 Epoch 1/3
Batch 0: Reward: -6.6916, Loss: 45.2956, Revenue: 0.3662, LoadBalance: 0.6468, Tasks: [S0:685(22.8%), S1:1177(39.1%), S2:1146(38.1%)], ActorGrad: 31.6840, CriticGrad: 363.6548, Advantage: μ=-1.181, σ=0.413, range=[-2.60, -0.25]

📊 Epoch 1 训练统计:
  平均奖励: -5.6321
  平均损失: 22.8005
  平均收益率: 0.3418
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -6.310, revenue_rate: 0.3357, efficiency: 0.1338, distance: 5.9727, memory: 0.0241, power: 0.1929
Test Summary - Avg reward: -6.374±1.559, revenue_rate: 0.3411±0.0430, efficiency: 0.1404, completion_rate: 0.4114, distance: 6.1752, memory: 0.0555, power: 0.2014
Load Balance - Avg balance score: 0.6355±0.1505
Task Distribution by Satellite:
  Satellite 1: 1498 tasks (37.19%)
  Satellite 2: 881 tasks (21.87%)
  Satellite 3: 1649 tasks (40.94%)
✅ 验证完成 - Epoch 1, reward: -6.374, revenue_rate: 0.3411, distance: 6.1752, memory: 0.0555, power: 0.2014
  ✅ 训练验证差距正常: 0.7415
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_07_20_07_56 (验证集奖励: -6.3736)

开始训练 Epoch 2/3
Batch 0: Reward: -5.0427, Loss: 11.3744, Revenue: 0.3212, LoadBalance: 0.7461, Tasks: [S0:759(30.4%), S1:870(34.9%), S2:867(34.7%)], ActorGrad: 35.3508, CriticGrad: 46.7115, Advantage: μ=-1.149, σ=0.497, range=[-2.23, -0.15]

📊 Epoch 2 训练统计:
  平均奖励: -5.0787
  平均损失: 12.1076
  平均收益率: 0.3188
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -5.813, revenue_rate: 0.3114, efficiency: 0.1146, distance: 5.3612, memory: 0.0583, power: 0.1775
Test Summary - Avg reward: -6.563±1.720, revenue_rate: 0.3598±0.0536, efficiency: 0.1596, completion_rate: 0.4382, distance: 6.5580, memory: 0.0522, power: 0.2115
Load Balance - Avg balance score: 0.6413±0.1486
Task Distribution by Satellite:
  Satellite 1: 1558 tasks (36.20%)
  Satellite 2: 1024 tasks (23.79%)
  Satellite 3: 1722 tasks (40.01%)
✅ 验证完成 - Epoch 2, reward: -6.563, revenue_rate: 0.3598, distance: 6.5580, memory: 0.0522, power: 0.2115
  ✅ 训练验证差距正常: 1.4841

开始训练 Epoch 3/3
Batch 0: Reward: -6.2074, Loss: 19.0929, Revenue: 0.3637, LoadBalance: 0.7794, Tasks: [S0:928(31.5%), S1:1050(35.7%), S2:966(32.8%)], ActorGrad: 55.1337, CriticGrad: 38.4062, Advantage: μ=-1.162, σ=0.464, range=[-2.23, 0.03]

📊 Epoch 3 训练统计:
  平均奖励: -4.9435
  平均损失: 10.1566
  平均收益率: 0.3109
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -5.140, revenue_rate: 0.2859, efficiency: 0.0940, distance: 4.7946, memory: 0.0226, power: 0.1568
Test Summary - Avg reward: -5.379±1.598, revenue_rate: 0.3039±0.0382, efficiency: 0.1079, completion_rate: 0.3542, distance: 5.1258, memory: 0.0194, power: 0.1692
Load Balance - Avg balance score: 0.6334±0.1712
Task Distribution by Satellite:
  Satellite 1: 1250 tasks (36.17%)
  Satellite 2: 805 tasks (23.29%)
  Satellite 3: 1401 tasks (40.54%)
✅ 验证完成 - Epoch 3, reward: -5.379, revenue_rate: 0.3039, distance: 5.1258, memory: 0.0194, power: 0.1692
  ✅ 训练验证差距正常: 0.4353
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_07_20_07_56 (验证集奖励: -5.3789)
训练完成

开始测试模型...
Test Batch 2/2, reward: -5.796, revenue_rate: 0.3135, efficiency: 0.1183, distance: 5.4628, memory: 0.0356, power: 0.1810
Test Summary - Avg reward: -5.790±1.635, revenue_rate: 0.3349±0.0440, efficiency: 0.1334, completion_rate: 0.3977, distance: 5.7385, memory: 0.0204, power: 0.1980
Load Balance - Avg balance score: 0.6424±0.1505
Task Distribution by Satellite:
  Satellite 1: 1385 tasks (35.59%)
  Satellite 2: 894 tasks (22.97%)
  Satellite 3: 1613 tasks (41.44%)
测试完成 - 平均奖励: -5.790, 平均星座收益率: 0.3349
✅ 模式 COMPETITIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_07_20_07_56
   平均奖励: -5.7897
   收益率: 0.3349

🚀 [3/3] 开始训练模式: HYBRID

============================================================
开始训练星座模式: HYBRID
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 64
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: hybrid
verbose: True
2025_08_07_20_16_17
使用模型: gpn_transformer
Actor参数数量: 178,724
Critic参数数量: 87,201

开始训练 Epoch 1/3
Batch 0: Reward: -8.2140, Loss: 77.8181, Revenue: 0.3429, LoadBalance: 0.6632, Tasks: [S0:636(23.1%), S1:1018(37.0%), S2:1098(39.9%)], ActorGrad: 37.8962, CriticGrad: 518.2913, Advantage: μ=-1.206, σ=0.333, range=[-1.97, -0.65]

📊 Epoch 1 训练统计:
  平均奖励: -8.1893
  平均损失: 59.6184
  平均收益率: 0.3441
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -8.203, revenue_rate: 0.3192, efficiency: 0.1205, distance: 5.3142, memory: 0.0459, power: 0.1828
Test Summary - Avg reward: -8.371±2.143, revenue_rate: 0.3309±0.0363, efficiency: 0.1317, completion_rate: 0.3975, distance: 5.7941, memory: 0.0427, power: 0.1934
Load Balance - Avg balance score: 0.6206±0.1759
Task Distribution by Satellite:
  Satellite 1: 1091 tasks (28.03%)
  Satellite 2: 1036 tasks (26.62%)
  Satellite 3: 1765 tasks (45.35%)
✅ 验证完成 - Epoch 1, reward: -8.371, revenue_rate: 0.3309, distance: 5.7941, memory: 0.0427, power: 0.1934
  ✅ 训练验证差距正常: 0.1813
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_07_20_16_17 (验证集奖励: -8.3706)

开始训练 Epoch 2/3
Batch 0: Reward: -7.8919, Loss: 42.3261, Revenue: 0.3320, LoadBalance: 0.6613, Tasks: [S0:644(24.5%), S1:1005(38.3%), S2:975(37.2%)], ActorGrad: 44.5079, CriticGrad: 139.7481, Advantage: μ=-1.184, σ=0.405, range=[-2.21, -0.36]

📊 Epoch 2 训练统计:
  平均奖励: -6.7229
  平均损失: 27.4010
  平均收益率: 0.3138
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -8.343, revenue_rate: 0.3444, efficiency: 0.1410, distance: 5.7410, memory: 0.0121, power: 0.1985
Test Summary - Avg reward: -8.898±2.452, revenue_rate: 0.3643±0.0460, efficiency: 0.1608, completion_rate: 0.4403, distance: 6.4967, memory: 0.0434, power: 0.2150
Load Balance - Avg balance score: 0.6241±0.1576
Task Distribution by Satellite:
  Satellite 1: 1780 tasks (41.20%)
  Satellite 2: 968 tasks (22.41%)
  Satellite 3: 1572 tasks (36.39%)
✅ 验证完成 - Epoch 2, reward: -8.898, revenue_rate: 0.3643, distance: 6.4967, memory: 0.0434, power: 0.2150
  ⚠️ 过拟合: 训练验证差距 = 2.1754

开始训练 Epoch 3/3
Batch 0: Reward: -6.1702, Loss: 20.3134, Revenue: 0.2789, LoadBalance: 0.7425, Tasks: [S0:764(35.1%), S1:717(33.0%), S2:695(31.9%)], ActorGrad: 52.7952, CriticGrad: 42.1737, Advantage: μ=-1.098, σ=0.602, range=[-2.87, 0.05]

📊 Epoch 3 训练统计:
  平均奖励: -6.6796
  平均损失: 24.4347
  平均收益率: 0.3164
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 2/2, reward: -7.092, revenue_rate: 0.2640, efficiency: 0.0817, distance: 4.2548, memory: 0.0150, power: 0.1556
Test Summary - Avg reward: -7.787±2.211, revenue_rate: 0.3091±0.0503, efficiency: 0.1188, completion_rate: 0.3787, distance: 5.4596, memory: 0.0596, power: 0.1867
Load Balance - Avg balance score: 0.6594±0.1469
Task Distribution by Satellite:
  Satellite 1: 1463 tasks (39.50%)
  Satellite 2: 878 tasks (23.70%)
  Satellite 3: 1363 tasks (36.80%)
✅ 验证完成 - Epoch 3, reward: -7.787, revenue_rate: 0.3091, distance: 5.4596, memory: 0.0596, power: 0.1867
  ✅ 训练验证差距正常: 1.1078
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_07_20_16_17 (验证集奖励: -7.7874)
训练完成

开始测试模型...
Test Batch 2/2, reward: -7.524, revenue_rate: 0.3064, efficiency: 0.1129, distance: 5.1472, memory: 0.0394, power: 0.1781
Test Summary - Avg reward: -8.008±2.421, revenue_rate: 0.3416±0.0516, efficiency: 0.1422, completion_rate: 0.4137, distance: 5.9834, memory: 0.0330, power: 0.2055
Load Balance - Avg balance score: 0.6669±0.1563
Task Distribution by Satellite:
  Satellite 1: 1562 tasks (38.59%)
  Satellite 2: 969 tasks (23.94%)
  Satellite 3: 1517 tasks (37.48%)
测试完成 - 平均奖励: -8.008, 平均星座收益率: 0.3416
✅ 模式 HYBRID 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_07_20_16_17
   平均奖励: -8.0077
   收益率: 0.3416

================================================================================
🎯 多模式训练总结
================================================================================
✅ COOPERATIVE: 奖励=-9.9584, 收益率=0.3629
✅ COMPETITIVE: 奖励=-5.7897, 收益率=0.3349
✅ HYBRID: 奖励=-8.0077, 收益率=0.3416

🏆 最佳模式: COMPETITIVE
   最高奖励: -5.7897
   对应收益率: 0.3349
   模型路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_07_20_07_56

🎉 所有模式训练完成！
