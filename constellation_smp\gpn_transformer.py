"""
基于Transformer的星座任务规划GPN模型
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from .transformer_encoder import ConstellationTransformerEncoder, ConstellationTransformerCritic

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class TransformerPointerNetwork(nn.Module):
    """基于Transformer的指针网络"""
    
    def __init__(self, d_model, n_heads=8, dropout=0.1):
        super(TransformerPointerNetwork, self).__init__()
        self.d_model = d_model
        
        # 查询生成网络 - 优化正则化（移除冗余BatchNorm）
        self.query_net = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout * 1.2)  # 增强dropout
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=n_heads,
            dropout=dropout
        )
        
        # 输出投影
        self.output_projection = nn.Linear(d_model, 1)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, query, context, mask=None):
        """
        query: (batch_size, d_model) - 当前状态查询
        context: (batch_size, d_model, seq_len) - 上下文特征
        mask: (batch_size, seq_len) - 掩码

        返回:
        logits: (batch_size, seq_len) - 选择概率
        """
        batch_size, d_model, seq_len = context.size()

        # 生成查询向量
        query = self.query_net(query)  # (batch_size, d_model)

        # 修复：统一维度处理 - 转换context到标准格式
        # 从 (batch_size, d_model, seq_len) 到 (batch_size, seq_len, d_model)
        context_reshaped = context.transpose(1, 2)  # (batch_size, seq_len, d_model)

        # 修复：使用标准的点积注意力机制
        # 扩展query以匹配序列长度
        query_expanded = query.unsqueeze(1)  # (batch_size, 1, d_model)

        # 计算注意力分数 - 使用批量矩阵乘法
        attention_scores = torch.bmm(query_expanded, context_reshaped.transpose(1, 2))  # (batch_size, 1, seq_len)
        attention_scores = attention_scores.squeeze(1)  # (batch_size, seq_len)

        # 缩放注意力分数
        attention_scores = attention_scores / (d_model ** 0.5)

        # 修复：简化的掩码处理逻辑
        if mask is not None:
            # 确保mask是2D张量 (batch_size, seq_len)
            if mask.dim() == 3:
                # 如果是3D掩码，使用逻辑或操作合并所有卫星的掩码
                mask = torch.any(mask, dim=2)  # (batch_size, seq_len)

            # 确保序列长度匹配
            if mask.size(1) != seq_len:
                if mask.size(1) > seq_len:
                    mask = mask[:, :seq_len]
                else:
                    # 填充mask（新任务默认可选）
                    padding = torch.ones(batch_size, seq_len - mask.size(1),
                                       device=mask.device, dtype=mask.dtype)
                    mask = torch.cat([mask, padding], dim=1)

            # 应用掩码（0表示不可选，设置为负无穷）
            attention_scores = attention_scores.masked_fill(mask == 0, -1e9)

        return attention_scores


class GPNTransformer(nn.Module):
    """基于Transformer的星座任务规划GPN模型"""
    
    def __init__(self, static_size, dynamic_size, d_model=256, n_heads=8, n_layers=6,
                 num_satellites=3, update_fn=None, mask_fn=None, num_nodes=50, 
                 dropout=0.1, constellation_mode='cooperative'):
        super(GPNTransformer, self).__init__()
        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.d_model = d_model
        self.num_satellites = num_satellites
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.constellation_mode = constellation_mode
        
        # Transformer编码器
        self.transformer_encoder = ConstellationTransformerEncoder(
            static_size + dynamic_size, d_model, n_heads, n_layers,
            d_model * 4, num_nodes * 2, dropout, num_satellites, constellation_mode
        )
        
        # 任务选择指针网络
        self.task_pointer = TransformerPointerNetwork(d_model, n_heads, dropout)
        
        # 卫星选择网络 - 优化正则化（移除冗余BatchNorm）
        self.satellite_selector = nn.Sequential(
            nn.Linear(d_model + d_model, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout * 1.2),  # 增强dropout
            nn.Linear(d_model, d_model // 2),  # 添加中间层
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, num_satellites)
        )
        
        # 状态更新网络 - 优化正则化（移除冗余BatchNorm）
        self.state_updater = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout * 1.2),  # 增强dropout
            nn.Linear(d_model, d_model // 2),  # 添加中间层
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model)
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, static, dynamic):
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)

        返回:
        tour_indices: (batch_size, tour_len) - 选择的任务索引
        satellite_indices: (batch_size, tour_len) - 执行任务的卫星索引
        tour_logp: (batch_size, tour_len) - 任务选择的对数概率
        """
        batch_size = static.size(0)
        seq_len = static.size(2)

        # 获取Transformer编码的特征
        constellation_features, satellite_features = self.transformer_encoder(static, dynamic)

        # 初始化状态
        current_state = torch.mean(constellation_features, dim=2)  # (batch_size, d_model)

        # 初始化结果列表
        tour_indices = []
        satellite_indices = []
        tour_logp = []

        # 初始化掩码
        mask = torch.ones(batch_size, seq_len, device=static.device)

        # 初始化卫星负载跟踪（修复：添加卫星负载跟踪）
        satellite_loads = torch.zeros(batch_size, self.num_satellites, device=static.device)
        
        # 迭代选择任务
        for step in range(seq_len):
            # 任务选择
            task_logits = self.task_pointer(current_state, constellation_features, mask)
            task_probs = F.softmax(task_logits, dim=1)
            
            # 采样任务
            task_dist = torch.distributions.Categorical(task_probs)
            chosen_task = task_dist.sample()
            task_log_prob = task_dist.log_prob(chosen_task)
            
            # 获取选中任务的特征
            chosen_task_features = torch.gather(
                constellation_features, 2, 
                chosen_task.unsqueeze(1).unsqueeze(2).expand(-1, self.d_model, -1)
            ).squeeze(2)  # (batch_size, d_model)
            
            # 卫星选择
            satellite_input = torch.cat([current_state, chosen_task_features], dim=1)
            satellite_logits = self.satellite_selector(satellite_input)
            satellite_probs = F.softmax(satellite_logits, dim=1)
            
            # 采样卫星
            satellite_dist = torch.distributions.Categorical(satellite_probs)
            chosen_satellite = satellite_dist.sample()
            satellite_log_prob = satellite_dist.log_prob(chosen_satellite)
            
            # 更新状态
            state_input = torch.cat([current_state, chosen_task_features], dim=1)
            current_state = self.state_updater(state_input)
            
            # 更新卫星负载
            for b in range(batch_size):
                satellite_loads[b, chosen_satellite[b]] += 1

            # 修复：简化和标准化掩码更新逻辑
            if self.mask_fn is not None:
                try:
                    # 标准化mask_fn调用接口
                    result = self.mask_fn(dynamic, static, chosen_task, chosen_satellite)

                    if isinstance(result, tuple) and len(result) == 2:
                        mask, _ = result
                    else:
                        mask = result

                    # 确保mask维度正确
                    if mask.dim() == 3:
                        # 对于3D掩码，使用逻辑与操作确保所有卫星都能执行任务
                        mask = torch.all(mask, dim=2)  # (batch_size, seq_len)

                    # 验证mask维度
                    if mask.size() != (batch_size, seq_len):
                        raise ValueError(f"Mask dimension mismatch: expected {(batch_size, seq_len)}, got {mask.size()}")

                except Exception as e:
                    # 记录错误并使用简单掩码策略
                    print(f"Warning: mask_fn failed with error {e}, using simple masking")
                    mask = mask.clone()  # 保持当前掩码状态
                    mask.scatter_(1, chosen_task.unsqueeze(1), 0)
            else:
                # 简单掩码：已选择的任务不能再选
                mask.scatter_(1, chosen_task.unsqueeze(1), 0)

            # 修复：标准化动态状态更新
            if self.update_fn is not None:
                try:
                    # 验证输入参数维度
                    assert chosen_task.dim() == 1 and chosen_task.size(0) == batch_size
                    assert chosen_satellite.dim() == 1 and chosen_satellite.size(0) == batch_size

                    # 调用动态更新函数
                    updated_dynamic = self.update_fn(static, dynamic, chosen_task, chosen_satellite)

                    # 验证输出维度
                    expected_shape = dynamic.shape
                    if updated_dynamic.shape != expected_shape:
                        print(f"Warning: dynamic update shape mismatch, expected {expected_shape}, got {updated_dynamic.shape}")
                        # 保持原始dynamic不变
                    else:
                        dynamic = updated_dynamic

                except Exception as e:
                    print(f"Warning: dynamic update failed with error {e}, keeping original dynamic state")
                    # 保持原始dynamic状态不变
            
            # 记录结果
            tour_indices.append(chosen_task)
            satellite_indices.append(chosen_satellite)
            tour_logp.append(task_log_prob + satellite_log_prob)
            
            # 检查是否所有任务都被掩码
            if mask.sum() == 0:
                break
        
        # 转换为张量
        tour_indices = torch.stack(tour_indices, dim=1)  # (batch_size, tour_len)
        satellite_indices = torch.stack(satellite_indices, dim=1)  # (batch_size, tour_len)
        tour_logp = torch.stack(tour_logp, dim=1)  # (batch_size, tour_len)
        
        return tour_indices, satellite_indices, tour_logp


class ConstellationTransformerStateCritic(nn.Module):
    """基于Transformer的星座状态评论家"""
    
    def __init__(self, static_size, dynamic_size, d_model=256, n_heads=8, n_layers=3,
                 num_satellites=3, constellation_mode='cooperative'):
        super(ConstellationTransformerStateCritic, self).__init__()
        
        # 使用Transformer编码器
        self.transformer_encoder = ConstellationTransformerEncoder(
            static_size + dynamic_size, d_model, n_heads, n_layers,
            d_model * 4, 1000, 0.1, num_satellites, constellation_mode
        )
        
        # 价值估计网络 - 优化正则化（移除冗余BatchNorm）
        self.value_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(0.15),  # 增强dropout
            nn.Linear(d_model // 2, d_model // 4),
            nn.LayerNorm(d_model // 4),
            nn.GELU(),
            nn.Dropout(0.12),  # 增强dropout
            nn.Linear(d_model // 4, 1)
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, static, dynamic):
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)
        
        返回:
        value: (batch_size,) - 状态价值估计
        """
        # 获取Transformer编码的特征
        constellation_features, _ = self.transformer_encoder(static, dynamic)
        
        # 全局特征聚合
        global_features = torch.mean(constellation_features, dim=2)  # (batch_size, d_model)
        
        # 价值估计
        value = self.value_head(global_features).squeeze(1)  # (batch_size,)
        
        return value


# 为了向后兼容，创建别名
GPNConstellationTransformer = GPNTransformer
ConstellationTransformerStateCritic = ConstellationTransformerStateCritic
