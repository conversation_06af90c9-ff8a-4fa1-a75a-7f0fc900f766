import torch
from gpn import GPN4SMP
from pn import PN4SMP
import validation.validation_data
import time
import datetime
import numpy as np
import os
from single_smp.single_smp import reward, render
from hyperparameter import args

# SMPDataset数据集的参数
plan_revenue_rate = []  # 收益率
infer_time = []  # 推理时间

# 固存和电量
Memory = validation.validation_data.Memory
Power = validation.validation_data.Power

# 推理的参数
m = 1  # 推理次数
infer_num_nodes = validation.validation_data.seq_len  # 每个序列中任务数量
RENDER = True  # 是否绘制推理结果

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
device_num = 0
print(device, device_num)
if device == 'cuda':
    torch.cuda.set_device(device_num)
    torch.backends.cudnn.enabled = False
# gpn + indrnn  2021_06_05_12_06_48
# gpn + lstm  2021_06_05_17_03_45
# pn + indrnn 2021_06_04_22_23_12
path = '../single_smp/single_smp100/2021_06_05_12_06_48/actor.pt'

if args.model == 'gpn':
    actor = GPN4SMP(args.static_size,
                    args.dynamic_size,
                    args.hidden_size,
                    args.task,
                    args.rnn,
                    args.num_layers,
                    validation.validation_data.update_dynamic,
                    validation.validation_data.update_mask,
                    args.num_nodes,
                    ).to(device)

elif args.model == 'pn':
    actor = PN4SMP(args.static_size,
                   args.dynamic_size,
                   args.hidden_size,
                   args.task,
                   validation.validation_data.update_dynamic,
                   validation.validation_data.update_mask,
                   args.num_layers,
                   args.dropout,
                   args.attention,
                   args.n_head,
                   args.rnn,
                   args.num_nodes,
                   ).to(device)

actor.load_state_dict(torch.load(path, device))
actor.eval()

if __name__ == '__main__':

    now = '%s' % datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    save_dir = ('infer%d' % infer_num_nodes)
    save_dir = os.path.join(save_dir, str(args.model) + str(args.rnn) + now)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    infer_dir = os.path.join(save_dir, 'infer_log.txt')
    with open(infer_dir, 'a+') as f:
        f.write(
            'infer_num_nodes: ' + str(infer_num_nodes) + '\n'
            + str(args.model) + '+' + str(args.rnn) + '\n'
            + 'memory: ' + str(validation.validation_data.Memory) + '\n'
            + 'path: ' + path + '\n'
        )
    memory_surplus = Memory  # 初始提供的存储
    power_surplus = Power  # 初始提供的电量
    total_tour_indices = []  # 索引
    static, dynamic = validation.validation_data.static, validation.validation_data.dynamic

    static = static[:, :, :infer_num_nodes].clone().to(device)
    dynamic = dynamic[:, :, :infer_num_nodes].clone().to(device)
    dynamic[:, 2, :] = memory_surplus
    dynamic[:, 3, :] = power_surplus
    static_record = static.clone()
    dynamic_record = dynamic.clone()
    infer_total_time = 0.
    print('total revenue:', torch.sum(static[:, 4, :], dim=1).item())
    print('total memory need:', torch.sum(static[:, 5, :], dim=1).item())
    print('total power need:', torch.sum(static[:, 6, :], dim=1).item())
    print('memory supply:', memory_surplus)
    print('power supply:', power_surplus)
    for i in range(m):
        start = time.time()
        tour_indices, _ = actor.forward(static, dynamic)
        reward_value, revenue_rate, distance, power = reward(static, tour_indices)
        end = time.time()
        infer_total_time += (end - start)
        print(
            'sat%s, reward_value: %2.3f, revenue_rate: %2.3f, distance: %2.3f, power: %2.3f' %
            (i, reward_value, revenue_rate, distance, power))
        memory_surplus -= torch.sum(static[:, 5, tour_indices.squeeze()], dim=1).item()
        power_surplus -= torch.sum(static[:, 6, tour_indices.squeeze()], dim=1).item()
        if memory_surplus < 0.0:
            memory_surplus = 0.0
        if power_surplus < 0.0:
            power_surplus = 0.0
        print('sat%s infer time:' % i, infer_total_time)
        print('sat%s infer tour_indices: ' % i, tour_indices)
        print('sat%s revenue: ' % i, torch.sum(static[:, 4, tour_indices.squeeze()], dim=1).item())
        print('sat%s memory surplus: %.4f' % (i, memory_surplus))
        print('sat%s power surplus: %.4f' % (i, power_surplus))
        dynamic[:, 1, tour_indices.squeeze()] = 0.
        static[:, 4, tour_indices.squeeze()] = 0.
        name = os.path.join(save_dir,
                            'single_smp_infer_result{}_{}_{}.png'.format(static.size(2), i, 1))
        render(static, tour_indices,
               name,
               i)
        total_tour_indices.append(tour_indices)
    total_tour_indices_tensor = torch.cat(total_tour_indices, dim=1).squeeze().cpu()
    total_tour_indices_numpy = total_tour_indices_tensor.numpy()
    unique_tour_indices_numpy = np.unique(total_tour_indices_numpy)
    unique_tour_indices_tensor = torch.from_numpy(unique_tour_indices_numpy)

    print('finish tasks: ', unique_tour_indices_tensor)
    print('finish tasks total num: ', unique_tour_indices_numpy.shape[0])
    print('plan total revenue: ', torch.sum(static_record[:, 4, unique_tour_indices_tensor], dim=1).item())

    print('plan revenue rate%s: %.2f%s, '
          'infer total time: %.2f ' % (1,
                                       (torch.sum(static_record[:, 4,
                                                  unique_tour_indices_tensor],
                                                  dim=1).item()) / (
                                           torch.sum(static_record[:, 4, :],
                                                     dim=1).item()) * 100.0, '%',
                                       infer_total_time))

    plan_revenue_rate.append((torch.sum(static_record[:, 4, unique_tour_indices_tensor], dim=1).item()) / (
        torch.sum(static_record[:, 4, :], dim=1).item()) * 100.0)
    infer_time.append(infer_total_time)

    with open(infer_dir, 'a+') as f:
        f.write('plan revenue rate%s: %.2f%s, '
                'infer total time: %.2f' % (1,
                                            (torch.sum(static_record[:, 4, unique_tour_indices_tensor],
                                                       dim=1).item()) / (
                                                torch.sum(static_record[:, 4, :], dim=1).item()) * 100.0,
                                            '%', infer_total_time) + '\n')

    mean_plan_revenue_rate = np.mean(plan_revenue_rate)
    mean_infer_time = np.mean(infer_time[1:])
    with open(infer_dir, 'a+') as f:
        f.write('mean_plan_revenue_rate: %.2f%s, ''mean_infer_time: %.2f' % (
            float(mean_plan_revenue_rate), '%', float(mean_infer_time)))
    print('mean_plan_revenue_rate: %.2f%s, ''mean_infer_time: %.2f' % (
        float(mean_plan_revenue_rate), '%', float(mean_infer_time)))
