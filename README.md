# 卫星任务规划(SMP)模型优化

本项目对基于GPN和IndRNN的卫星任务规划(SMP)模型进行了一系列优化，旨在提高reward指标。现已扩展支持卫星星座的任务规划。

## 主要改进

### 1. 注意力机制优化
- 实现了更高效的`MultiHead_Additive_Attention`类
- 添加了层归一化和残差连接
- 使用了更好的参数初始化方法（Kaiming初始化）
- 添加了注意力dropout以防止过拟合

### 2. IndRNN网络优化
- 改进了`IndRNN_Net`类，添加了输入输出投影层
- 添加了更有效的残差连接机制
- 增加了层归一化以提高训练稳定性
- 优化了参数初始化方法

### 3. 超参数优化
- 增大了梯度裁剪阈值（从1.0到2.0）
- 略微增加了dropout率（从0.1到0.15）
- 增大了学习率（actor_lr和critic_lr从5e-5到8e-5）
- 增大了批量大小（从8到16）
- 增加了网络层数（从1层到2层）

### 4. 学习率调度优化
- 使用余弦退火学习率调度器替代原有的StepLR
- 实现了学习率周期性重启策略，有助于跳出局部最优
- 为actor和critic分别设置了学习率调度器

### 5. 星座任务规划扩展
- 支持多颗卫星协同任务规划
- 实现了星座级别的编码器，融合多颗卫星的状态信息
- 添加了卫星间注意力机制，实现卫星间信息交互
- 实现了任务选择和卫星选择的双重决策机制
- 优化了奖励函数，考虑整个星座的全局收益

## 使用方法

### 单星任务规划
```bash
python train.py --task single_smp --model gpn --rnn indrnn --num_nodes 100 --batch_size 32 --epochs 3
```

### 星座任务规划
```bash
python train_constellation.py --task constellation_smp --model gpn --rnn indrnn --num_nodes 100 --batch_size 32 --epochs 3 --num_satellites 3
```

## 星座参数配置
可以通过以下参数配置星座任务规划：
- `--num_satellites`: 星座中的卫星数量，默认为3
- `--constellation_mode`: 星座工作模式，可选值为cooperative(协同)、competitive(竞争)、hybrid(混合)，默认为cooperative
- `--task_sharing`: 是否允许卫星间共享任务信息，默认为True
- `--communication_delay`: 星座内卫星间通信延迟，默认为0.01
- `--satellite_distance`: 卫星间平均距离（归一化），默认为0.5

## 预期效果
这些优化预计将带来以下改进：
1. 更快的收敛速度
2. 更高的reward值
3. 更好的泛化能力
4. 更稳定的训练过程
5. 星座协同带来的全局任务规划效率提升 