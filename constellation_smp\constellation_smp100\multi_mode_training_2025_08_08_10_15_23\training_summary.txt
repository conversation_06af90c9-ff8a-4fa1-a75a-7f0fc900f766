🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_08_10_15_23
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_08_10_21_42
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: -14.9249, Loss: 261.6315, Revenue: 0.4350, LoadBalance: 0.4674, Tasks: [S0:746(44.0%), S1:241(14.2%), S2:709(41.8%)], ActorGrad: 62.7931, CriticGrad: 850.0485, Advantage: μ=-1.748, σ=0.265, range=[-2.32, -1.16]
Epoch 1, Batch 50/3125, loss: 64.108↓, reward: -9.385↑, critic_reward: -2.369, revenue_rate: 0.3275, distance: 5.7605, memory: 0.0529, power: 0.1897, lr: 0.000050, took: 210.626s
Batch 50: Reward: -9.2458, Loss: 42.2116, Revenue: 0.3107, LoadBalance: 0.6733, Tasks: [S0:382(33.2%), S1:452(39.2%), S2:318(27.6%)], ActorGrad: 70.4275, CriticGrad: 73.7892, Advantage: μ=-1.618, σ=0.724, range=[-3.02, -0.09]
Epoch 1, Batch 100/3125, loss: 32.158↓, reward: -8.433↑, critic_reward: -3.467, revenue_rate: 0.3126, distance: 5.4716, memory: 0.0423, power: 0.1791, lr: 0.000050, took: 192.660s
Batch 100: Reward: -8.2583, Loss: 31.1207, Revenue: 0.2883, LoadBalance: 0.7346, Tasks: [S0:388(35.7%), S1:301(27.7%), S2:399(36.7%)], ActorGrad: 36.9325, CriticGrad: 60.7046, Advantage: μ=-1.455, σ=1.020, range=[-3.92, 1.08]
Epoch 1, Batch 150/3125, loss: 29.911↓, reward: -8.415↑, critic_reward: -3.732, revenue_rate: 0.3144, distance: 5.4464, memory: 0.0311, power: 0.1801, lr: 0.000050, took: 194.159s
Batch 150: Reward: -8.8465, Loss: 31.5428, Revenue: 0.3080, LoadBalance: 0.6851, Tasks: [S0:356(31.8%), S1:404(36.1%), S2:360(32.1%)], ActorGrad: 36.9188, CriticGrad: 60.9370, Advantage: μ=-1.594, σ=0.777, range=[-3.22, -0.26]
Epoch 1, Batch 200/3125, loss: 26.997↓, reward: -8.403↑, critic_reward: -3.982, revenue_rate: 0.3196, distance: 5.5796, memory: 0.0403, power: 0.1847, lr: 0.000050, took: 199.701s
Batch 200: Reward: -7.9785, Loss: 21.0936, Revenue: 0.3117, LoadBalance: 0.7459, Tasks: [S0:429(37.2%), S1:381(33.1%), S2:342(29.7%)], ActorGrad: 41.8268, CriticGrad: 46.1027, Advantage: μ=-1.584, σ=0.798, range=[-2.96, 0.02]
Epoch 1, Batch 250/3125, loss: 23.870↑, reward: -8.244↑, critic_reward: -4.107, revenue_rate: 0.3201, distance: 5.5454, memory: 0.0409, power: 0.1841, lr: 0.000050, took: 197.747s
Batch 250: Reward: -8.2627, Loss: 23.9812, Revenue: 0.3122, LoadBalance: 0.7584, Tasks: [S0:357(31.0%), S1:385(33.4%), S2:410(35.6%)], ActorGrad: 26.6541, CriticGrad: 53.0333, Advantage: μ=-1.500, σ=0.950, range=[-3.34, 0.16]
Epoch 1, Batch 300/3125, loss: 24.469↑, reward: -8.373↓, critic_reward: -4.252, revenue_rate: 0.3158, distance: 5.4758, memory: 0.0356, power: 0.1810, lr: 0.000050, took: 193.792s
Batch 300: Reward: -8.7935, Loss: 29.9808, Revenue: 0.3125, LoadBalance: 0.7175, Tasks: [S0:342(29.7%), S1:356(30.9%), S2:454(39.4%)], ActorGrad: 48.1780, CriticGrad: 50.4918, Advantage: μ=-1.505, σ=0.942, range=[-3.52, 0.06]
Epoch 1, Batch 350/3125, loss: 22.633↓, reward: -8.258↑, critic_reward: -4.386, revenue_rate: 0.3084, distance: 5.3022, memory: 0.0458, power: 0.1765, lr: 0.000050, took: 189.352s
Batch 350: Reward: -7.5970, Loss: 14.7928, Revenue: 0.2847, LoadBalance: 0.7600, Tasks: [S0:363(33.4%), S1:363(33.4%), S2:362(33.3%)], ActorGrad: 23.4081, CriticGrad: 36.0981, Advantage: μ=-1.484, σ=0.976, range=[-3.35, 0.34]
