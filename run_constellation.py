"""
卫星星座任务规划模型的入口脚本
"""
import os
import sys
import argparse
from train_constellation import train_constellation_smp

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='卫星星座任务规划')
    parser.add_argument('--mode', default='train', choices=['train', 'test'], 
                        help='运行模式：训练或测试')
    parser.add_argument('--num_satellites', type=int, default=3, 
                        help='星座中的卫星数量')
    parser.add_argument('--constellation_mode', default='cooperative', 
                        choices=['cooperative', 'competitive', 'hybrid'], 
                        help='星座工作模式')
    parser.add_argument('--task_sharing', action='store_true', default=True, 
                        help='是否允许卫星间共享任务信息')
    parser.add_argument('--communication_delay', type=float, default=0.01, 
                        help='星座内卫星间通信延迟')
    parser.add_argument('--satellite_distance', type=float, default=0.5, 
                        help='卫星间平均距离（归一化）')
    parser.add_argument('--checkpoint', default=None, 
                        help='加载模型检查点的路径')
    parser.add_argument('--verbose', action='store_true', default=True,
                        help='是否打印详细训练信息')
    
    args = parser.parse_args()
    
    # 设置命令行参数
    sys_args = ['--task', 'constellation_smp']
    
    if args.mode == 'test':
        sys_args.append('--test')
    
    if args.checkpoint:
        sys_args.extend(['--checkpoint', args.checkpoint])
    
    sys_args.extend(['--num_satellites', str(args.num_satellites)])
    sys_args.extend(['--constellation_mode', args.constellation_mode])
    
    if args.task_sharing:
        sys_args.append('--task_sharing')
    
    if args.verbose:
        sys_args.append('--verbose')
    
    sys_args.extend(['--communication_delay', str(args.communication_delay)])
    sys_args.extend(['--satellite_distance', str(args.satellite_distance)])
    
    # 更新系统参数
    sys.argv.extend(sys_args)
    
    # 运行训练或测试
    print("启动卫星星座任务规划...")
    print(f"星座配置: {args.num_satellites}颗卫星, 模式: {args.constellation_mode}")
    print(f"详细模式: {'开启' if args.verbose else '关闭'}")
    train_constellation_smp() 