🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_07_20_42_10
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 5e-05
critic_lr: 5e-05
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_07_20_48_18
使用模型: gpn_transformer
Actor参数数量: 1,102,148
Critic参数数量: 541,633

开始训练 Epoch 1/3
Batch 0: Reward: -21.0555, Loss: 502.1132, Revenue: 1.0000, LoadBalance: 0.5068, Tasks: [S0:1353(42.7%), S1:475(15.0%), S2:1340(42.3%)], ActorGrad: 48.1907, CriticGrad: 1265.7207, Advantage: μ=-1.762, σ=0.145, range=[-2.14, -1.52]
Epoch 1, Batch 50/3125, loss: 295.341↓, reward: -19.185↑, critic_reward: -2.433, revenue_rate: 1.0000, distance: 16.3567, memory: 0.1962, power: 0.4902, lr: 0.000050, took: 478.282s
Batch 50: Reward: -19.5774, Loss: 278.5558, Revenue: 1.0000, LoadBalance: 0.6381, Tasks: [S0:799(25.2%), S1:897(28.3%), S2:1472(46.5%)], ActorGrad: 44.8935, CriticGrad: 203.5655, Advantage: μ=-1.738, σ=0.330, range=[-2.44, -0.84]
Epoch 1, Batch 100/3125, loss: 216.858↑, reward: -17.912↓, critic_reward: -3.465, revenue_rate: 1.0000, distance: 16.4744, memory: 0.1947, power: 0.4903, lr: 0.000050, took: 474.475s
Batch 100: Reward: -20.5053, Loss: 290.9311, Revenue: 1.0000, LoadBalance: 0.5641, Tasks: [S0:789(24.9%), S1:1563(49.3%), S2:816(25.8%)], ActorGrad: 37.8202, CriticGrad: 217.1458, Advantage: μ=-1.735, σ=0.345, range=[-2.54, -1.09]
Epoch 1, Batch 150/3125, loss: 192.834↓, reward: -17.324↑, critic_reward: -3.733, revenue_rate: 1.0000, distance: 16.4217, memory: 0.1944, power: 0.4904, lr: 0.000050, took: 475.230s
Batch 150: Reward: -16.4814, Loss: 163.1890, Revenue: 1.0000, LoadBalance: 0.8056, Tasks: [S0:909(28.7%), S1:1132(35.7%), S2:1127(35.6%)], ActorGrad: 38.2969, CriticGrad: 154.5592, Advantage: μ=-1.731, σ=0.364, range=[-2.46, -0.86]
Epoch 1, Batch 200/3125, loss: 161.630↓, reward: -16.445↑, critic_reward: -3.988, revenue_rate: 1.0000, distance: 16.4142, memory: 0.1967, power: 0.4906, lr: 0.000050, took: 474.820s
Batch 200: Reward: -16.6662, Loss: 159.0627, Revenue: 1.0000, LoadBalance: 0.8206, Tasks: [S0:1020(32.2%), S1:945(29.8%), S2:1203(38.0%)], ActorGrad: 34.5779, CriticGrad: 154.4688, Advantage: μ=-1.746, σ=0.284, range=[-2.30, -1.21]
Epoch 1, Batch 250/3125, loss: 175.292↑, reward: -17.086↓, critic_reward: -4.132, revenue_rate: 1.0000, distance: 16.3198, memory: 0.1994, power: 0.4893, lr: 0.000050, took: 474.048s
Batch 250: Reward: -19.1418, Loss: 233.2147, Revenue: 1.0000, LoadBalance: 0.6518, Tasks: [S0:1144(36.1%), S1:1348(42.6%), S2:676(21.3%)], ActorGrad: 30.4989, CriticGrad: 178.3817, Advantage: μ=-1.739, σ=0.320, range=[-2.27, -0.89]
Epoch 1, Batch 300/3125, loss: 167.623↓, reward: -16.931↑, critic_reward: -4.283, revenue_rate: 1.0000, distance: 16.3967, memory: 0.1978, power: 0.4899, lr: 0.000050, took: 475.495s
Batch 300: Reward: -16.1592, Loss: 152.0470, Revenue: 1.0000, LoadBalance: 0.8221, Tasks: [S0:1021(32.2%), S1:1180(37.2%), S2:967(30.5%)], ActorGrad: 33.7104, CriticGrad: 130.2180, Advantage: μ=-1.723, σ=0.400, range=[-2.60, -1.07]
Epoch 1, Batch 350/3125, loss: 148.296↓, reward: -16.296↑, critic_reward: -4.395, revenue_rate: 1.0000, distance: 16.4090, memory: 0.1959, power: 0.4905, lr: 0.000050, took: 473.217s
Batch 350: Reward: -16.5805, Loss: 152.6758, Revenue: 1.0000, LoadBalance: 0.8356, Tasks: [S0:1103(34.8%), S1:1114(35.2%), S2:951(30.0%)], ActorGrad: 27.1074, CriticGrad: 134.2307, Advantage: μ=-1.737, σ=0.332, range=[-2.33, -0.94]
Epoch 1, Batch 400/3125, loss: 157.554↑, reward: -16.794↓, critic_reward: -4.530, revenue_rate: 1.0000, distance: 16.3979, memory: 0.1963, power: 0.4905, lr: 0.000050, took: 476.676s
Batch 400: Reward: -17.2567, Loss: 168.8746, Revenue: 1.0000, LoadBalance: 0.7414, Tasks: [S0:824(26.0%), S1:1045(33.0%), S2:1299(41.0%)], ActorGrad: 33.8630, CriticGrad: 144.5019, Advantage: μ=-1.723, σ=0.401, range=[-2.56, -1.08]
Epoch 1, Batch 450/3125, loss: 183.703↑, reward: -17.795↓, critic_reward: -4.643, revenue_rate: 1.0000, distance: 16.4235, memory: 0.1972, power: 0.4900, lr: 0.000050, took: 472.626s
Batch 450: Reward: -19.9167, Loss: 241.2263, Revenue: 1.0000, LoadBalance: 0.6005, Tasks: [S0:1445(45.6%), S1:647(20.4%), S2:1076(34.0%)], ActorGrad: 27.0532, CriticGrad: 185.0424, Advantage: μ=-1.747, σ=0.273, range=[-2.36, -1.27]
Epoch 1, Batch 500/3125, loss: 157.796↓, reward: -17.005↑, critic_reward: -4.758, revenue_rate: 1.0000, distance: 16.4256, memory: 0.1952, power: 0.4898, lr: 0.000050, took: 476.253s
Batch 500: Reward: -16.5598, Loss: 145.6305, Revenue: 1.0000, LoadBalance: 0.8334, Tasks: [S0:1081(34.1%), S1:984(31.1%), S2:1103(34.8%)], ActorGrad: 28.8835, CriticGrad: 129.8061, Advantage: μ=-1.735, σ=0.343, range=[-2.38, -1.14]
Epoch 1, Batch 550/3125, loss: 153.031↓, reward: -16.913↑, critic_reward: -4.854, revenue_rate: 1.0000, distance: 16.3909, memory: 0.1990, power: 0.4902, lr: 0.000050, took: 474.752s
Batch 550: Reward: -16.3831, Loss: 135.6511, Revenue: 1.0000, LoadBalance: 0.8207, Tasks: [S0:957(30.2%), S1:1070(33.8%), S2:1141(36.0%)], ActorGrad: 21.7026, CriticGrad: 131.9693, Advantage: μ=-1.737, σ=0.336, range=[-2.57, -0.76]
Epoch 1, Batch 600/3125, loss: 166.533↑, reward: -17.521↓, critic_reward: -4.908, revenue_rate: 1.0000, distance: 16.4281, memory: 0.1953, power: 0.4913, lr: 0.000050, took: 474.072s
Batch 600: Reward: -18.2075, Loss: 185.3407, Revenue: 1.0000, LoadBalance: 0.7428, Tasks: [S0:1287(40.6%), S1:879(27.7%), S2:1002(31.6%)], ActorGrad: 23.3046, CriticGrad: 148.1716, Advantage: μ=-1.733, σ=0.357, range=[-2.53, -1.08]
Epoch 1, Batch 650/3125, loss: 142.553↓, reward: -16.650↑, critic_reward: -5.027, revenue_rate: 1.0000, distance: 16.4125, memory: 0.1972, power: 0.4894, lr: 0.000050, took: 473.887s
Batch 650: Reward: -16.2468, Loss: 129.0159, Revenue: 1.0000, LoadBalance: 0.8210, Tasks: [S0:1200(37.9%), S1:994(31.4%), S2:974(30.7%)], ActorGrad: 29.9625, CriticGrad: 127.2349, Advantage: μ=-1.728, σ=0.381, range=[-2.65, -1.00]
Epoch 1, Batch 700/3125, loss: 135.440↑, reward: -16.448↓, critic_reward: -5.100, revenue_rate: 1.0000, distance: 16.4299, memory: 0.1977, power: 0.4884, lr: 0.000050, took: 473.711s
Batch 700: Reward: -16.8384, Loss: 141.7362, Revenue: 1.0000, LoadBalance: 0.7891, Tasks: [S0:1260(39.8%), S1:902(28.5%), S2:1006(31.8%)], ActorGrad: 23.0634, CriticGrad: 144.4490, Advantage: μ=-1.730, σ=0.370, range=[-2.43, -1.07]
Epoch 1, Batch 750/3125, loss: 133.267↓, reward: -16.453↑, critic_reward: -5.193, revenue_rate: 1.0000, distance: 16.4042, memory: 0.1948, power: 0.4906, lr: 0.000050, took: 473.492s
Batch 750: Reward: -16.2012, Loss: 128.4411, Revenue: 1.0000, LoadBalance: 0.8327, Tasks: [S0:1140(36.0%), S1:1003(31.7%), S2:1025(32.4%)], ActorGrad: 22.1224, CriticGrad: 132.6006, Advantage: μ=-1.719, σ=0.420, range=[-2.43, -0.75]
Epoch 1, Batch 800/3125, loss: 128.493↑, reward: -16.318↓, critic_reward: -5.290, revenue_rate: 1.0000, distance: 16.4153, memory: 0.1955, power: 0.4887, lr: 0.000050, took: 473.516s
Batch 800: Reward: -15.8742, Loss: 120.1687, Revenue: 1.0000, LoadBalance: 0.8061, Tasks: [S0:990(31.2%), S1:1124(35.5%), S2:1054(33.3%)], ActorGrad: 25.8216, CriticGrad: 120.7264, Advantage: μ=-1.713, σ=0.443, range=[-2.53, -0.64]
Epoch 1, Batch 850/3125, loss: 124.494↑, reward: -16.255↓, critic_reward: -5.398, revenue_rate: 1.0000, distance: 16.4296, memory: 0.1970, power: 0.4889, lr: 0.000050, took: 474.182s
Batch 850: Reward: -15.9806, Loss: 122.2840, Revenue: 1.0000, LoadBalance: 0.8229, Tasks: [S0:1095(34.6%), S1:1059(33.4%), S2:1014(32.0%)], ActorGrad: 29.8088, CriticGrad: 119.8714, Advantage: μ=-1.714, σ=0.439, range=[-2.49, -0.73]
Epoch 1, Batch 900/3125, loss: 151.602↑, reward: -17.354↓, critic_reward: -5.408, revenue_rate: 1.0000, distance: 16.3839, memory: 0.1943, power: 0.4893, lr: 0.000050, took: 473.549s
Batch 900: Reward: -16.9357, Loss: 139.7292, Revenue: 1.0000, LoadBalance: 0.8338, Tasks: [S0:1040(32.8%), S1:1147(36.2%), S2:981(31.0%)], ActorGrad: 29.7811, CriticGrad: 135.1133, Advantage: μ=-1.718, σ=0.424, range=[-2.55, -0.87]
Epoch 1, Batch 950/3125, loss: 140.067↑, reward: -16.988↓, critic_reward: -5.489, revenue_rate: 1.0000, distance: 16.4350, memory: 0.1968, power: 0.4903, lr: 0.000050, took: 473.784s
Batch 950: Reward: -18.2702, Loss: 166.7999, Revenue: 1.0000, LoadBalance: 0.7264, Tasks: [S0:788(24.9%), S1:1088(34.3%), S2:1292(40.8%)], ActorGrad: 23.4826, CriticGrad: 150.0115, Advantage: μ=-1.736, σ=0.338, range=[-2.36, -1.10]
Epoch 1, Batch 1000/3125, loss: 138.167↓, reward: -17.041↑, critic_reward: -5.601, revenue_rate: 1.0000, distance: 16.4398, memory: 0.1968, power: 0.4905, lr: 0.000050, took: 473.587s
Batch 1000: Reward: -17.1050, Loss: 139.6054, Revenue: 1.0000, LoadBalance: 0.7682, Tasks: [S0:851(26.9%), S1:1071(33.8%), S2:1246(39.3%)], ActorGrad: 17.8744, CriticGrad: 135.0708, Advantage: μ=-1.728, σ=0.379, range=[-2.62, -1.11]
Epoch 1, Batch 1050/3125, loss: 121.919↓, reward: -16.440↑, critic_reward: -5.704, revenue_rate: 1.0000, distance: 16.3902, memory: 0.1947, power: 0.4908, lr: 0.000050, took: 476.041s
Batch 1050: Reward: -16.2368, Loss: 115.7532, Revenue: 1.0000, LoadBalance: 0.8461, Tasks: [S0:1025(32.4%), S1:1064(33.6%), S2:1079(34.1%)], ActorGrad: 21.8892, CriticGrad: 125.9407, Advantage: μ=-1.713, σ=0.444, range=[-2.70, -0.77]
Epoch 1, Batch 1100/3125, loss: 119.754↓, reward: -16.392↑, critic_reward: -5.771, revenue_rate: 1.0000, distance: 16.4665, memory: 0.1945, power: 0.4892, lr: 0.000050, took: 473.122s
Batch 1100: Reward: -16.4234, Loss: 121.1762, Revenue: 1.0000, LoadBalance: 0.8229, Tasks: [S0:920(29.0%), S1:1186(37.4%), S2:1062(33.5%)], ActorGrad: 18.3001, CriticGrad: 125.8387, Advantage: μ=-1.724, σ=0.399, range=[-2.62, -1.04]
Epoch 1, Batch 1150/3125, loss: 125.059↓, reward: -16.727↑, critic_reward: -5.863, revenue_rate: 1.0000, distance: 16.4598, memory: 0.1967, power: 0.4900, lr: 0.000050, took: 475.092s
Batch 1150: Reward: -16.5056, Loss: 122.4924, Revenue: 1.0000, LoadBalance: 0.8221, Tasks: [S0:1165(36.8%), S1:1024(32.3%), S2:979(30.9%)], ActorGrad: 16.5416, CriticGrad: 126.9915, Advantage: μ=-1.718, σ=0.424, range=[-2.62, -0.95]
Epoch 1, Batch 1200/3125, loss: 125.405↑, reward: -16.819↓, critic_reward: -5.947, revenue_rate: 1.0000, distance: 16.4158, memory: 0.1949, power: 0.4901, lr: 0.000050, took: 471.447s
Batch 1200: Reward: -16.7346, Loss: 122.3558, Revenue: 1.0000, LoadBalance: 0.7697, Tasks: [S0:1298(41.0%), S1:924(29.2%), S2:946(29.9%)], ActorGrad: 32.5845, CriticGrad: 130.4530, Advantage: μ=-1.722, σ=0.407, range=[-2.63, -0.87]
Epoch 1, Batch 1250/3125, loss: 116.777↓, reward: -16.521↑, critic_reward: -6.029, revenue_rate: 1.0000, distance: 16.4663, memory: 0.1972, power: 0.4910, lr: 0.000050, took: 475.066s
Batch 1250: Reward: -16.0254, Loss: 100.8263, Revenue: 1.0000, LoadBalance: 0.8460, Tasks: [S0:1118(35.3%), S1:1094(34.5%), S2:956(30.2%)], ActorGrad: 15.8692, CriticGrad: 123.6484, Advantage: μ=-1.722, σ=0.405, range=[-2.54, -0.76]
Epoch 1, Batch 1300/3125, loss: 111.903↑, reward: -16.333↓, critic_reward: -6.091, revenue_rate: 1.0000, distance: 16.4157, memory: 0.1963, power: 0.4892, lr: 0.000050, took: 472.964s
Batch 1300: Reward: -16.9046, Loss: 121.2765, Revenue: 1.0000, LoadBalance: 0.8014, Tasks: [S0:1092(34.5%), S1:1172(37.0%), S2:904(28.5%)], ActorGrad: 17.9632, CriticGrad: 131.9476, Advantage: μ=-1.717, σ=0.428, range=[-2.57, -0.93]
Epoch 1, Batch 1350/3125, loss: 115.173↓, reward: -16.539↑, critic_reward: -6.133, revenue_rate: 1.0000, distance: 16.4316, memory: 0.1968, power: 0.4904, lr: 0.000050, took: 475.196s
Batch 1350: Reward: -15.9613, Loss: 102.8568, Revenue: 1.0000, LoadBalance: 0.8367, Tasks: [S0:1033(32.6%), S1:1028(32.4%), S2:1107(34.9%)], ActorGrad: 37.2761, CriticGrad: 120.6577, Advantage: μ=-1.717, σ=0.429, range=[-2.70, -0.93]
Epoch 1, Batch 1400/3125, loss: 103.832↓, reward: -16.100↑, critic_reward: -6.250, revenue_rate: 1.0000, distance: 16.3786, memory: 0.1957, power: 0.4893, lr: 0.000050, took: 472.346s
Batch 1400: Reward: -16.6052, Loss: 110.5950, Revenue: 1.0000, LoadBalance: 0.7865, Tasks: [S0:1026(32.4%), S1:1198(37.8%), S2:944(29.8%)], ActorGrad: 36.0434, CriticGrad: 133.9728, Advantage: μ=-1.722, σ=0.407, range=[-2.64, -0.84]
Epoch 1, Batch 1450/3125, loss: 102.276↓, reward: -16.108↑, critic_reward: -6.319, revenue_rate: 1.0000, distance: 16.3908, memory: 0.1962, power: 0.4905, lr: 0.000050, took: 475.812s
Batch 1450: Reward: -16.1685, Loss: 107.8401, Revenue: 1.0000, LoadBalance: 0.8498, Tasks: [S0:1042(32.9%), S1:1125(35.5%), S2:1001(31.6%)], ActorGrad: 16.4673, CriticGrad: 121.5648, Advantage: μ=-1.702, σ=0.485, range=[-2.86, -0.63]
Epoch 1, Batch 1500/3125, loss: 104.829↓, reward: -16.326↑, critic_reward: -6.397, revenue_rate: 1.0000, distance: 16.4693, memory: 0.1949, power: 0.4900, lr: 0.000050, took: 473.272s
Batch 1500: Reward: -17.0180, Loss: 117.6151, Revenue: 1.0000, LoadBalance: 0.8022, Tasks: [S0:919(29.0%), S1:1073(33.9%), S2:1176(37.1%)], ActorGrad: 25.5809, CriticGrad: 137.6436, Advantage: μ=-1.714, σ=0.440, range=[-2.56, -0.82]
Epoch 1, Batch 1550/3125, loss: 103.202↓, reward: -16.291↑, critic_reward: -6.465, revenue_rate: 1.0000, distance: 16.4731, memory: 0.1974, power: 0.4903, lr: 0.000050, took: 472.445s
Batch 1550: Reward: -16.8014, Loss: 109.1091, Revenue: 1.0000, LoadBalance: 0.7939, Tasks: [S0:992(31.3%), S1:1211(38.2%), S2:965(30.5%)], ActorGrad: 13.9849, CriticGrad: 137.0146, Advantage: μ=-1.715, σ=0.437, range=[-2.40, -0.78]
Epoch 1, Batch 1600/3125, loss: 106.122↑, reward: -16.500↓, critic_reward: -6.546, revenue_rate: 1.0000, distance: 16.4179, memory: 0.1953, power: 0.4890, lr: 0.000050, took: 473.899s
Batch 1600: Reward: -17.3216, Loss: 123.2781, Revenue: 1.0000, LoadBalance: 0.7944, Tasks: [S0:908(28.7%), S1:1041(32.9%), S2:1219(38.5%)], ActorGrad: 22.8365, CriticGrad: 132.8182, Advantage: μ=-1.713, σ=0.442, range=[-2.81, -0.78]
Epoch 1, Batch 1650/3125, loss: 96.944↓, reward: -16.193↑, critic_reward: -6.684, revenue_rate: 1.0000, distance: 16.4109, memory: 0.1968, power: 0.4895, lr: 0.000050, took: 473.616s
Batch 1650: Reward: -16.8270, Loss: 109.0976, Revenue: 1.0000, LoadBalance: 0.8188, Tasks: [S0:1118(35.3%), S1:1078(34.0%), S2:972(30.7%)], ActorGrad: 16.7453, CriticGrad: 128.1011, Advantage: μ=-1.728, σ=0.380, range=[-2.58, -0.93]
Epoch 1, Batch 1700/3125, loss: 97.538↓, reward: -16.239↑, critic_reward: -6.717, revenue_rate: 1.0000, distance: 16.4394, memory: 0.1937, power: 0.4892, lr: 0.000050, took: 476.737s
Batch 1700: Reward: -16.1000, Loss: 99.2144, Revenue: 1.0000, LoadBalance: 0.8499, Tasks: [S0:1066(33.6%), S1:1085(34.2%), S2:1017(32.1%)], ActorGrad: 15.0360, CriticGrad: 120.6197, Advantage: μ=-1.681, σ=0.554, range=[-3.09, -0.96]
Epoch 1, Batch 1750/3125, loss: 101.952↓, reward: -16.539↑, critic_reward: -6.807, revenue_rate: 1.0000, distance: 16.4254, memory: 0.1972, power: 0.4892, lr: 0.000050, took: 475.578s
Batch 1750: Reward: -16.7743, Loss: 103.8126, Revenue: 1.0000, LoadBalance: 0.8091, Tasks: [S0:994(31.4%), S1:1176(37.1%), S2:998(31.5%)], ActorGrad: 27.4086, CriticGrad: 126.7260, Advantage: μ=-1.710, σ=0.457, range=[-2.80, -0.95]
Epoch 1, Batch 1800/3125, loss: 99.922↓, reward: -16.514↑, critic_reward: -6.877, revenue_rate: 1.0000, distance: 16.4716, memory: 0.1978, power: 0.4903, lr: 0.000050, took: 474.346s
Batch 1800: Reward: -15.6451, Loss: 81.2777, Revenue: 1.0000, LoadBalance: 0.8386, Tasks: [S0:1097(34.6%), S1:1036(32.7%), S2:1035(32.7%)], ActorGrad: 17.2390, CriticGrad: 111.2391, Advantage: μ=-1.710, σ=0.454, range=[-2.86, -1.03]
Epoch 1, Batch 1850/3125, loss: 97.873↑, reward: -16.484↓, critic_reward: -6.956, revenue_rate: 1.0000, distance: 16.4475, memory: 0.1969, power: 0.4899, lr: 0.000050, took: 473.286s
Batch 1850: Reward: -15.7464, Loss: 83.8779, Revenue: 1.0000, LoadBalance: 0.8301, Tasks: [S0:1036(32.7%), S1:1072(33.8%), S2:1060(33.5%)], ActorGrad: 38.5490, CriticGrad: 112.9437, Advantage: μ=-1.704, σ=0.477, range=[-2.79, -0.71]
Epoch 1, Batch 1900/3125, loss: 91.010↑, reward: -16.184↓, critic_reward: -6.995, revenue_rate: 1.0000, distance: 16.4165, memory: 0.1969, power: 0.4902, lr: 0.000050, took: 476.363s
Batch 1900: Reward: -15.4143, Loss: 74.6745, Revenue: 1.0000, LoadBalance: 0.8639, Tasks: [S0:1023(32.3%), S1:1101(34.8%), S2:1044(33.0%)], ActorGrad: 16.0520, CriticGrad: 104.9375, Advantage: μ=-1.706, σ=0.472, range=[-2.60, -0.81]
Epoch 1, Batch 1950/3125, loss: 92.066↑, reward: -16.342↓, critic_reward: -7.115, revenue_rate: 1.0000, distance: 16.4084, memory: 0.1973, power: 0.4901, lr: 0.000050, took: 473.012s
Batch 1950: Reward: -16.5116, Loss: 95.4883, Revenue: 1.0000, LoadBalance: 0.7925, Tasks: [S0:1135(35.8%), S1:950(30.0%), S2:1083(34.2%)], ActorGrad: 15.5512, CriticGrad: 119.3478, Advantage: μ=-1.680, σ=0.559, range=[-3.28, -0.72]
Epoch 1, Batch 2000/3125, loss: 91.863↓, reward: -16.422↑, critic_reward: -7.204, revenue_rate: 1.0000, distance: 16.3652, memory: 0.1956, power: 0.4901, lr: 0.000050, took: 475.443s
Batch 2000: Reward: -16.5572, Loss: 96.5848, Revenue: 1.0000, LoadBalance: 0.8460, Tasks: [S0:971(30.7%), S1:1122(35.4%), S2:1075(33.9%)], ActorGrad: 14.2323, CriticGrad: 119.8059, Advantage: μ=-1.705, σ=0.473, range=[-2.53, -0.41]
Epoch 1, Batch 2050/3125, loss: 91.465↓, reward: -16.421↓, critic_reward: -7.218, revenue_rate: 1.0000, distance: 16.4489, memory: 0.1975, power: 0.4900, lr: 0.000050, took: 472.103s
Batch 2050: Reward: -15.9296, Loss: 80.9367, Revenue: 1.0000, LoadBalance: 0.8473, Tasks: [S0:970(30.6%), S1:1053(33.2%), S2:1145(36.1%)], ActorGrad: 13.7135, CriticGrad: 109.4174, Advantage: μ=-1.714, σ=0.440, range=[-2.79, -1.00]
Epoch 1, Batch 2100/3125, loss: 86.128↑, reward: -16.205↓, critic_reward: -7.287, revenue_rate: 1.0000, distance: 16.4623, memory: 0.1956, power: 0.4902, lr: 0.000050, took: 475.120s
Batch 2100: Reward: -15.8661, Loss: 80.7006, Revenue: 1.0000, LoadBalance: 0.8497, Tasks: [S0:1116(35.2%), S1:1023(32.3%), S2:1029(32.5%)], ActorGrad: 15.2817, CriticGrad: 109.2495, Advantage: μ=-1.690, σ=0.526, range=[-3.02, -0.43]
Epoch 1, Batch 2150/3125, loss: 80.973↓, reward: -16.033↓, critic_reward: -7.401, revenue_rate: 1.0000, distance: 16.3843, memory: 0.1945, power: 0.4913, lr: 0.000050, took: 472.096s
Batch 2150: Reward: -16.5727, Loss: 93.1262, Revenue: 1.0000, LoadBalance: 0.8438, Tasks: [S0:1056(33.3%), S1:1016(32.1%), S2:1096(34.6%)], ActorGrad: 14.8845, CriticGrad: 125.9980, Advantage: μ=-1.677, σ=0.568, range=[-2.89, -0.64]
Epoch 1, Batch 2200/3125, loss: 88.123↓, reward: -16.478↓, critic_reward: -7.482, revenue_rate: 1.0000, distance: 16.4191, memory: 0.1973, power: 0.4900, lr: 0.000050, took: 475.508s
Batch 2200: Reward: -16.6490, Loss: 88.7604, Revenue: 1.0000, LoadBalance: 0.8211, Tasks: [S0:977(30.8%), S1:1196(37.8%), S2:995(31.4%)], ActorGrad: 18.2482, CriticGrad: 117.0511, Advantage: μ=-1.716, σ=0.430, range=[-2.62, -0.76]
Epoch 1, Batch 2250/3125, loss: 84.176↓, reward: -16.305↑, critic_reward: -7.518, revenue_rate: 1.0000, distance: 16.4526, memory: 0.1950, power: 0.4897, lr: 0.000050, took: 472.190s
Batch 2250: Reward: -16.0832, Loss: 76.5241, Revenue: 1.0000, LoadBalance: 0.8345, Tasks: [S0:1044(33.0%), S1:982(31.0%), S2:1142(36.0%)], ActorGrad: 15.5566, CriticGrad: 108.7268, Advantage: μ=-1.689, σ=0.529, range=[-3.37, -0.35]
Epoch 1, Batch 2300/3125, loss: 87.832↑, reward: -16.655↓, critic_reward: -7.684, revenue_rate: 1.0000, distance: 16.4222, memory: 0.1960, power: 0.4910, lr: 0.000050, took: 475.698s
Batch 2300: Reward: -16.1149, Loss: 81.2061, Revenue: 1.0000, LoadBalance: 0.8445, Tasks: [S0:1101(34.8%), S1:1059(33.4%), S2:1008(31.8%)], ActorGrad: 14.1112, CriticGrad: 113.5280, Advantage: μ=-1.667, σ=0.597, range=[-2.95, -0.71]
Epoch 1, Batch 2350/3125, loss: 74.673↓, reward: -16.004↑, critic_reward: -7.756, revenue_rate: 1.0000, distance: 16.3408, memory: 0.1965, power: 0.4893, lr: 0.000050, took: 473.282s
Batch 2350: Reward: -17.0743, Loss: 93.1219, Revenue: 1.0000, LoadBalance: 0.8062, Tasks: [S0:1032(32.6%), S1:1184(37.4%), S2:952(30.1%)], ActorGrad: 24.8888, CriticGrad: 125.9094, Advantage: μ=-1.701, σ=0.489, range=[-2.98, -0.98]
Epoch 1, Batch 2400/3125, loss: 78.059↓, reward: -16.234↑, critic_reward: -7.793, revenue_rate: 1.0000, distance: 16.3860, memory: 0.1952, power: 0.4900, lr: 0.000050, took: 475.972s
Batch 2400: Reward: -16.5393, Loss: 88.9241, Revenue: 1.0000, LoadBalance: 0.8173, Tasks: [S0:977(30.8%), S1:1101(34.8%), S2:1090(34.4%)], ActorGrad: 14.4602, CriticGrad: 114.7263, Advantage: μ=-1.691, σ=0.525, range=[-2.64, -0.74]
Epoch 1, Batch 2450/3125, loss: 78.557↓, reward: -16.335↑, critic_reward: -7.886, revenue_rate: 1.0000, distance: 16.4433, memory: 0.1957, power: 0.4889, lr: 0.000050, took: 474.097s
Batch 2450: Reward: -16.1271, Loss: 71.7761, Revenue: 1.0000, LoadBalance: 0.8476, Tasks: [S0:1084(34.2%), S1:981(31.0%), S2:1103(34.8%)], ActorGrad: 19.3405, CriticGrad: 109.3280, Advantage: μ=-1.689, σ=0.529, range=[-2.83, -0.66]
Epoch 1, Batch 2500/3125, loss: 72.216↓, reward: -16.020↑, critic_reward: -7.922, revenue_rate: 1.0000, distance: 16.3488, memory: 0.1937, power: 0.4898, lr: 0.000050, took: 474.132s
Batch 2500: Reward: -15.0635, Loss: 56.8077, Revenue: 1.0000, LoadBalance: 0.8600, Tasks: [S0:1116(35.2%), S1:1069(33.7%), S2:983(31.0%)], ActorGrad: 24.3673, CriticGrad: 93.5771, Advantage: μ=-1.668, σ=0.596, range=[-2.98, -0.54]
Epoch 1, Batch 2550/3125, loss: 72.044↑, reward: -16.109↓, critic_reward: -8.031, revenue_rate: 1.0000, distance: 16.3599, memory: 0.1959, power: 0.4893, lr: 0.000050, took: 474.755s
Batch 2550: Reward: -16.6138, Loss: 74.8003, Revenue: 1.0000, LoadBalance: 0.8289, Tasks: [S0:1034(32.6%), S1:950(30.0%), S2:1184(37.4%)], ActorGrad: 18.9166, CriticGrad: 118.2508, Advantage: μ=-1.700, σ=0.493, range=[-2.98, -0.83]
Epoch 1, Batch 2600/3125, loss: 72.130↓, reward: -16.180↑, critic_reward: -8.084, revenue_rate: 1.0000, distance: 16.4416, memory: 0.1964, power: 0.4904, lr: 0.000050, took: 473.142s
Batch 2600: Reward: -16.4407, Loss: 74.7594, Revenue: 1.0000, LoadBalance: 0.8051, Tasks: [S0:1041(32.9%), S1:992(31.3%), S2:1135(35.8%)], ActorGrad: 25.8967, CriticGrad: 116.3530, Advantage: μ=-1.712, σ=0.447, range=[-2.55, -0.62]
Epoch 1, Batch 2650/3125, loss: 69.286↓, reward: -16.118↑, critic_reward: -8.199, revenue_rate: 1.0000, distance: 16.4200, memory: 0.1970, power: 0.4913, lr: 0.000050, took: 482.619s
Batch 2650: Reward: -16.4674, Loss: 74.3763, Revenue: 1.0000, LoadBalance: 0.8542, Tasks: [S0:1027(32.4%), S1:1065(33.6%), S2:1076(34.0%)], ActorGrad: 16.2513, CriticGrad: 111.0153, Advantage: μ=-1.677, σ=0.568, range=[-3.15, -0.74]
Epoch 1, Batch 2700/3125, loss: 67.478↓, reward: -16.028↑, critic_reward: -8.264, revenue_rate: 1.0000, distance: 16.3955, memory: 0.1958, power: 0.4895, lr: 0.000050, took: 475.969s
Batch 2700: Reward: -16.2419, Loss: 68.1510, Revenue: 1.0000, LoadBalance: 0.8232, Tasks: [S0:956(30.2%), S1:1066(33.6%), S2:1146(36.2%)], ActorGrad: 30.9906, CriticGrad: 107.9863, Advantage: μ=-1.685, σ=0.544, range=[-3.24, -0.38]
Epoch 1, Batch 2750/3125, loss: 65.473↓, reward: -16.034↑, critic_reward: -8.365, revenue_rate: 1.0000, distance: 16.3777, memory: 0.1977, power: 0.4905, lr: 0.000050, took: 475.280s
Batch 2750: Reward: -16.5302, Loss: 74.5132, Revenue: 1.0000, LoadBalance: 0.8138, Tasks: [S0:936(29.5%), S1:1153(36.4%), S2:1079(34.1%)], ActorGrad: 17.2921, CriticGrad: 111.0280, Advantage: μ=-1.681, σ=0.557, range=[-3.19, -0.59]
Epoch 1, Batch 2800/3125, loss: 67.383↓, reward: -16.169↑, critic_reward: -8.394, revenue_rate: 1.0000, distance: 16.4049, memory: 0.1950, power: 0.4879, lr: 0.000050, took: 472.753s
Batch 2800: Reward: -16.5301, Loss: 66.6509, Revenue: 1.0000, LoadBalance: 0.8230, Tasks: [S0:1061(33.5%), S1:1136(35.9%), S2:971(30.7%)], ActorGrad: 19.6136, CriticGrad: 111.9151, Advantage: μ=-1.701, σ=0.487, range=[-2.64, -0.89]
Epoch 1, Batch 2850/3125, loss: 68.145↓, reward: -16.317↑, critic_reward: -8.519, revenue_rate: 1.0000, distance: 16.4030, memory: 0.1969, power: 0.4885, lr: 0.000050, took: 475.291s
Batch 2850: Reward: -16.1220, Loss: 58.7056, Revenue: 1.0000, LoadBalance: 0.8486, Tasks: [S0:1138(35.9%), S1:1019(32.2%), S2:1011(31.9%)], ActorGrad: 23.0610, CriticGrad: 106.0929, Advantage: μ=-1.703, σ=0.483, range=[-2.97, -0.47]
Epoch 1, Batch 2900/3125, loss: 65.403↑, reward: -16.180↑, critic_reward: -8.556, revenue_rate: 1.0000, distance: 16.3831, memory: 0.1958, power: 0.4888, lr: 0.000050, took: 471.906s
Batch 2900: Reward: -16.0705, Loss: 65.3678, Revenue: 1.0000, LoadBalance: 0.8453, Tasks: [S0:1009(31.8%), S1:1072(33.8%), S2:1087(34.3%)], ActorGrad: 18.0310, CriticGrad: 102.9397, Advantage: μ=-1.688, σ=0.534, range=[-3.05, -0.84]
Epoch 1, Batch 2950/3125, loss: 63.052↑, reward: -16.138↓, critic_reward: -8.655, revenue_rate: 1.0000, distance: 16.4422, memory: 0.1971, power: 0.4906, lr: 0.000050, took: 475.724s
Batch 2950: Reward: -16.2143, Loss: 60.9674, Revenue: 1.0000, LoadBalance: 0.8236, Tasks: [S0:1095(34.6%), S1:967(30.5%), S2:1106(34.9%)], ActorGrad: 19.8447, CriticGrad: 101.3465, Advantage: μ=-1.666, σ=0.600, range=[-3.28, -0.29]
Epoch 1, Batch 3000/3125, loss: 58.952↓, reward: -15.982↑, critic_reward: -8.732, revenue_rate: 1.0000, distance: 16.3681, memory: 0.1955, power: 0.4891, lr: 0.000050, took: 471.327s
Batch 3000: Reward: -15.7730, Loss: 51.8032, Revenue: 1.0000, LoadBalance: 0.8581, Tasks: [S0:1043(32.9%), S1:1106(34.9%), S2:1019(32.2%)], ActorGrad: 14.7461, CriticGrad: 96.9875, Advantage: μ=-1.674, σ=0.576, range=[-2.95, -0.30]
Epoch 1, Batch 3050/3125, loss: 64.253↑, reward: -16.424↓, critic_reward: -8.816, revenue_rate: 1.0000, distance: 16.4490, memory: 0.1951, power: 0.4905, lr: 0.000050, took: 476.771s
Batch 3050: Reward: -16.4332, Loss: 58.5875, Revenue: 1.0000, LoadBalance: 0.8236, Tasks: [S0:1054(33.3%), S1:1040(32.8%), S2:1074(33.9%)], ActorGrad: 13.1870, CriticGrad: 106.7791, Advantage: μ=-1.696, σ=0.508, range=[-2.65, -0.55]
Epoch 1, Batch 3100/3125, loss: 58.059↓, reward: -16.046↑, critic_reward: -8.905, revenue_rate: 1.0000, distance: 16.3939, memory: 0.1967, power: 0.4895, lr: 0.000050, took: 472.937s
Batch 3100: Reward: -15.6981, Loss: 53.2274, Revenue: 1.0000, LoadBalance: 0.8087, Tasks: [S0:991(31.3%), S1:981(31.0%), S2:1196(37.8%)], ActorGrad: 20.0077, CriticGrad: 95.8218, Advantage: μ=-1.636, σ=0.680, range=[-2.93, 0.17]

📊 Epoch 1 训练统计:
  平均奖励: -16.5322
  平均损失: 112.6419
  平均收益率: 1.0000
  当前学习率: 0.000050

🔍 开始验证...
Test Batch 10/313, reward: -16.423, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.5578, memory: 0.1855, power: 0.4859
Test Batch 20/313, reward: -15.384, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.0714, memory: 0.1992, power: 0.4965
Test Batch 30/313, reward: -16.940, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.8132, memory: 0.1983, power: 0.4837
Test Batch 40/313, reward: -15.871, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.2619, memory: 0.1972, power: 0.4894
Test Batch 50/313, reward: -16.174, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.5298, memory: 0.2118, power: 0.4944
Test Batch 60/313, reward: -15.852, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.2635, memory: 0.1839, power: 0.4900
Test Batch 70/313, reward: -16.521, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.6731, memory: 0.2171, power: 0.4778
Test Batch 80/313, reward: -16.460, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.3383, memory: 0.1963, power: 0.4867
Test Batch 90/313, reward: -16.153, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.5487, memory: 0.1937, power: 0.4875
Test Batch 100/313, reward: -15.762, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.1967, memory: 0.1990, power: 0.4918
Test Batch 110/313, reward: -16.689, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.4184, memory: 0.1841, power: 0.4972
Test Batch 120/313, reward: -15.259, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.0878, memory: 0.1886, power: 0.4957
Test Batch 130/313, reward: -16.887, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.7837, memory: 0.1986, power: 0.4880
Test Batch 140/313, reward: -16.498, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.5764, memory: 0.1931, power: 0.4889
Test Batch 150/313, reward: -15.858, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.3302, memory: 0.1905, power: 0.4802
Test Batch 160/313, reward: -16.504, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.3670, memory: 0.1821, power: 0.4895
Test Batch 170/313, reward: -15.979, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.2461, memory: 0.2029, power: 0.4892
Test Batch 180/313, reward: -16.323, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.3578, memory: 0.1879, power: 0.4840
Test Batch 190/313, reward: -16.730, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.8001, memory: 0.1873, power: 0.4878
Test Batch 200/313, reward: -15.873, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.4996, memory: 0.1968, power: 0.4924
Test Batch 210/313, reward: -16.334, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.6852, memory: 0.2090, power: 0.4918
Test Batch 220/313, reward: -16.079, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.1642, memory: 0.2034, power: 0.4998
Test Batch 230/313, reward: -15.796, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.1021, memory: 0.1842, power: 0.4895
Test Batch 240/313, reward: -17.031, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.8017, memory: 0.1932, power: 0.4859
Test Batch 250/313, reward: -16.654, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.7697, memory: 0.1806, power: 0.4954
Test Batch 260/313, reward: -16.365, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.4801, memory: 0.1882, power: 0.4796
Test Batch 270/313, reward: -15.977, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.4656, memory: 0.1902, power: 0.4910
Test Batch 280/313, reward: -16.199, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.3335, memory: 0.1953, power: 0.4918
Test Batch 290/313, reward: -15.442, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.2428, memory: 0.1867, power: 0.4855
Test Batch 300/313, reward: -16.281, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.7226, memory: 0.1833, power: 0.4899
Test Batch 310/313, reward: -15.652, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.2192, memory: 0.2181, power: 0.4887
Test Batch 313/313, reward: -16.879, revenue_rate: 1.0000, efficiency: 0.9900, distance: 16.5350, memory: 0.2037, power: 0.4935
Test Summary - Avg reward: -16.143±2.386, revenue_rate: 1.0000±0.0000, efficiency: 0.9900, completion_rate: 0.9900, distance: 16.4183, memory: 0.1962, power: 0.4900
Load Balance - Avg balance score: 0.8371±0.0837
Task Distribution by Satellite:
  Satellite 1: 346327 tasks (34.98%)
  Satellite 2: 336178 tasks (33.96%)
  Satellite 3: 307495 tasks (31.06%)
✅ 验证完成 - Epoch 1, reward: -16.143, revenue_rate: 1.0000, distance: 16.4183, memory: 0.1962, power: 0.4900
  ✅ 训练验证差距正常: -0.3895
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_07_20_48_18 (验证集奖励: -16.1427)

开始训练 Epoch 2/3
Batch 0: Reward: -16.4252, Loss: 58.1262, Revenue: 1.0000, LoadBalance: 0.8442, Tasks: [S0:1101(34.8%), S1:1017(32.1%), S2:1050(33.1%)], ActorGrad: 22.6023, CriticGrad: 103.0469, Advantage: μ=-1.665, σ=0.602, range=[-2.69, -0.47]
Epoch 2, Batch 50/3125, loss: 59.969↓, reward: -16.248↑, critic_reward: -8.982, revenue_rate: 1.0000, distance: 16.3878, memory: 0.1970, power: 0.4916, lr: 0.000050, took: 476.574s
Batch 50: Reward: -16.5147, Loss: 64.9438, Revenue: 1.0000, LoadBalance: 0.8214, Tasks: [S0:1149(36.3%), S1:1042(32.9%), S2:977(30.8%)], ActorGrad: 21.8575, CriticGrad: 105.0160, Advantage: μ=-1.640, σ=0.671, range=[-2.71, 0.02]
Epoch 2, Batch 100/3125, loss: 55.255↓, reward: -16.034↓, critic_reward: -9.111, revenue_rate: 1.0000, distance: 16.3617, memory: 0.1974, power: 0.4911, lr: 0.000050, took: 475.271s
Batch 100: Reward: -15.5810, Loss: 42.8576, Revenue: 1.0000, LoadBalance: 0.8385, Tasks: [S0:1070(33.8%), S1:1072(33.8%), S2:1026(32.4%)], ActorGrad: 19.8647, CriticGrad: 91.0816, Advantage: μ=-1.650, σ=0.644, range=[-3.27, -0.38]
Epoch 2, Batch 150/3125, loss: 52.716↑, reward: -16.021↓, critic_reward: -9.252, revenue_rate: 1.0000, distance: 16.3769, memory: 0.1955, power: 0.4902, lr: 0.000050, took: 472.776s
Batch 150: Reward: -16.1277, Loss: 50.9365, Revenue: 1.0000, LoadBalance: 0.8618, Tasks: [S0:982(31.0%), S1:1077(34.0%), S2:1109(35.0%)], ActorGrad: 21.5231, CriticGrad: 99.7443, Advantage: μ=-1.660, σ=0.618, range=[-3.21, -0.63]
Epoch 2, Batch 200/3125, loss: 54.584↓, reward: -16.202↓, critic_reward: -9.279, revenue_rate: 1.0000, distance: 16.4591, memory: 0.1959, power: 0.4905, lr: 0.000050, took: 475.615s
Batch 200: Reward: -16.0578, Loss: 50.8135, Revenue: 1.0000, LoadBalance: 0.8314, Tasks: [S0:1103(34.8%), S1:1058(33.4%), S2:1007(31.8%)], ActorGrad: 39.3849, CriticGrad: 95.5230, Advantage: μ=-1.686, σ=0.539, range=[-2.52, -0.56]
Epoch 2, Batch 250/3125, loss: 56.518↑, reward: -16.357↓, critic_reward: -9.337, revenue_rate: 1.0000, distance: 16.3826, memory: 0.1952, power: 0.4896, lr: 0.000050, took: 472.619s
Batch 250: Reward: -16.5243, Loss: 53.8664, Revenue: 1.0000, LoadBalance: 0.8370, Tasks: [S0:985(31.1%), S1:1093(34.5%), S2:1090(34.4%)], ActorGrad: 18.9916, CriticGrad: 101.2131, Advantage: μ=-1.680, σ=0.559, range=[-2.92, -0.21]
Epoch 2, Batch 300/3125, loss: 55.018↑, reward: -16.327↑, critic_reward: -9.421, revenue_rate: 1.0000, distance: 16.4609, memory: 0.1968, power: 0.4906, lr: 0.000050, took: 475.328s
Batch 300: Reward: -16.0366, Loss: 53.3655, Revenue: 1.0000, LoadBalance: 0.8512, Tasks: [S0:1049(33.1%), S1:1095(34.6%), S2:1024(32.3%)], ActorGrad: 14.0096, CriticGrad: 92.0021, Advantage: μ=-1.586, σ=0.794, range=[-2.91, 0.25]
Epoch 2, Batch 350/3125, loss: 50.079↓, reward: -16.136↓, critic_reward: -9.538, revenue_rate: 1.0000, distance: 16.4065, memory: 0.1973, power: 0.4891, lr: 0.000050, took: 474.719s
Batch 350: Reward: -16.0289, Loss: 47.8628, Revenue: 1.0000, LoadBalance: 0.8319, Tasks: [S0:1047(33.0%), S1:1133(35.8%), S2:988(31.2%)], ActorGrad: 21.1176, CriticGrad: 92.0946, Advantage: μ=-1.614, σ=0.734, range=[-3.51, -0.20]
Epoch 2, Batch 400/3125, loss: 51.032↑, reward: -16.229↓, critic_reward: -9.607, revenue_rate: 1.0000, distance: 16.4699, memory: 0.1955, power: 0.4905, lr: 0.000050, took: 472.443s
Batch 400: Reward: -15.8949, Loss: 51.2674, Revenue: 1.0000, LoadBalance: 0.8504, Tasks: [S0:1115(35.2%), S1:977(30.8%), S2:1076(34.0%)], ActorGrad: 13.2405, CriticGrad: 91.2241, Advantage: μ=-1.680, σ=0.558, range=[-3.38, -0.66]
Epoch 2, Batch 450/3125, loss: 51.059↑, reward: -16.256↓, critic_reward: -9.644, revenue_rate: 1.0000, distance: 16.4681, memory: 0.1950, power: 0.4889, lr: 0.000050, took: 475.130s
Batch 450: Reward: -16.5145, Loss: 54.4395, Revenue: 1.0000, LoadBalance: 0.8139, Tasks: [S0:984(31.1%), S1:1174(37.1%), S2:1010(31.9%)], ActorGrad: 17.3608, CriticGrad: 98.5896, Advantage: μ=-1.632, σ=0.690, range=[-2.87, -0.08]
Epoch 2, Batch 500/3125, loss: 48.056↓, reward: -16.144↑, critic_reward: -9.787, revenue_rate: 1.0000, distance: 16.4260, memory: 0.1959, power: 0.4906, lr: 0.000050, took: 472.689s
Batch 500: Reward: -15.8831, Loss: 49.0804, Revenue: 1.0000, LoadBalance: 0.8287, Tasks: [S0:1081(34.1%), S1:1033(32.6%), S2:1054(33.3%)], ActorGrad: 12.5098, CriticGrad: 91.6888, Advantage: μ=-1.575, σ=0.815, range=[-3.06, 0.01]
Epoch 2, Batch 550/3125, loss: 47.746↓, reward: -16.239↑, critic_reward: -9.880, revenue_rate: 1.0000, distance: 16.4927, memory: 0.1945, power: 0.4900, lr: 0.000050, took: 475.743s
Batch 550: Reward: -15.8284, Loss: 38.5837, Revenue: 1.0000, LoadBalance: 0.8334, Tasks: [S0:1093(34.5%), S1:1118(35.3%), S2:957(30.2%)], ActorGrad: 16.3819, CriticGrad: 87.3916, Advantage: μ=-1.632, σ=0.690, range=[-3.04, -0.37]
Epoch 2, Batch 600/3125, loss: 46.173↑, reward: -16.113↑, critic_reward: -9.900, revenue_rate: 1.0000, distance: 16.3836, memory: 0.1958, power: 0.4886, lr: 0.000050, took: 472.706s
Batch 600: Reward: -16.2857, Loss: 46.9264, Revenue: 1.0000, LoadBalance: 0.8408, Tasks: [S0:1062(33.5%), S1:1085(34.2%), S2:1021(32.2%)], ActorGrad: 28.1479, CriticGrad: 93.5560, Advantage: μ=-1.664, σ=0.607, range=[-3.11, -0.32]
Epoch 2, Batch 650/3125, loss: 43.162↑, reward: -15.931↓, critic_reward: -9.961, revenue_rate: 1.0000, distance: 16.3800, memory: 0.1939, power: 0.4891, lr: 0.000050, took: 474.596s
Batch 650: Reward: -15.7630, Loss: 35.3454, Revenue: 1.0000, LoadBalance: 0.8231, Tasks: [S0:997(31.5%), S1:1082(34.2%), S2:1089(34.4%)], ActorGrad: 17.0272, CriticGrad: 81.4898, Advantage: μ=-1.626, σ=0.705, range=[-3.22, -0.83]
Epoch 2, Batch 700/3125, loss: 43.016↓, reward: -16.043↑, critic_reward: -10.058, revenue_rate: 1.0000, distance: 16.4186, memory: 0.1949, power: 0.4891, lr: 0.000050, took: 471.444s
Batch 700: Reward: -16.0376, Loss: 43.8368, Revenue: 1.0000, LoadBalance: 0.8352, Tasks: [S0:1080(34.1%), S1:997(31.5%), S2:1091(34.4%)], ActorGrad: 23.6604, CriticGrad: 90.9643, Advantage: μ=-1.572, σ=0.821, range=[-3.15, 0.48]
Epoch 2, Batch 750/3125, loss: 43.231↓, reward: -16.123↑, critic_reward: -10.167, revenue_rate: 1.0000, distance: 16.4156, memory: 0.1958, power: 0.4910, lr: 0.000050, took: 475.889s
Batch 750: Reward: -15.9121, Loss: 40.0283, Revenue: 1.0000, LoadBalance: 0.8098, Tasks: [S0:1027(32.4%), S1:1070(33.8%), S2:1071(33.8%)], ActorGrad: 16.6520, CriticGrad: 92.5693, Advantage: μ=-1.623, σ=0.711, range=[-3.11, 0.21]
Epoch 2, Batch 800/3125, loss: 41.565↓, reward: -16.162↓, critic_reward: -10.319, revenue_rate: 1.0000, distance: 16.3899, memory: 0.1956, power: 0.4893, lr: 0.000050, took: 471.621s
Batch 800: Reward: -15.3162, Loss: 30.5405, Revenue: 1.0000, LoadBalance: 0.8539, Tasks: [S0:959(30.3%), S1:1099(34.7%), S2:1110(35.0%)], ActorGrad: 17.9025, CriticGrad: 73.6273, Advantage: μ=-1.595, σ=0.774, range=[-3.09, 0.13]
Epoch 2, Batch 850/3125, loss: 39.947↑, reward: -16.069↓, critic_reward: -10.367, revenue_rate: 1.0000, distance: 16.4121, memory: 0.1949, power: 0.4893, lr: 0.000050, took: 475.502s
Batch 850: Reward: -15.9290, Loss: 39.2428, Revenue: 1.0000, LoadBalance: 0.8360, Tasks: [S0:1131(35.7%), S1:1002(31.6%), S2:1035(32.7%)], ActorGrad: 24.0986, CriticGrad: 83.7841, Advantage: μ=-1.561, σ=0.844, range=[-3.78, 0.26]
